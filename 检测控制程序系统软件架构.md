# 检测控制程序系统软件架构设计

## 📋 文档概述

**项目名称：** 检测控制程序系统  
**架构版本：** v1.0  
**设计日期：** 2025年7月23日  
**核心目标：** 支持±10μm靶丸高精度定位的统一调度控制系统

---

## 🏗️ 系统总体架构设计

### 1.1 四层架构体系

基于软件设计书v3的架构，集成高精度定位控制能力：

```
┌─────────────────────────────────────────────────────────────┐
│                    上游接入层 (Integration Layer)              │
├─────────────────────────────────────────────────────────────┤
│  MES接口模块  │  工单接收器  │  数字孪生接口  │  外部系统API    │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                   控制核心层 (Control Core Layer)             │
├─────────────────────────────────────────────────────────────┤
│ 流程引擎平台内核 │ 高精度定位控制 │ 规则控制器 │ 插件管理器    │
│ 设备组调度模块   │ 6DOF协调控制   │ 表单编排器 │ 异常处理器    │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                   人工输入层 (Human Interface Layer)          │
├─────────────────────────────────────────────────────────────┤
│  PDA终端接口  │  平板终端UI  │  扫码确认  │  信息展示模块     │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                  执行控制层 (Execution Layer)                 │
├─────────────────────────────────────────────────────────────┤
│ 8套设备API接口 │ 伺服控制系统 │ 传感器采集 │ 状态监控模块    │
│ 高精度定位执行 │ 检测算法引擎 │ 数据采集器 │ 设备健康监控    │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 系统部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MES系统       │    │  数字孪生平台    │    │   外部监控系统   │
│                 │    │                 │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │ HTTP/JSON            │ MQTT                 │ HTTP
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────┐
│              检测控制程序系统主服务器                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  SpringBoot     │  │  流程引擎核心    │  │  数据管理模块    ││
│  │  应用服务       │  │                 │  │                 ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
          │ EtherCAT/TCP                    │ IPC/TCP
          ▼                                 ▼
┌─────────────────┐                ┌─────────────────┐
│ BECKHOFF控制器   │                │  现场PDA/平板    │
│ TwinCAT实时系统  │                │  Qt客户端       │
└─────────┬───────┘                └─────────────────┘
          │ 实时总线
          ▼
┌─────────────────────────────────────────────────────────────┐
│                    现场设备层                                │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────────────────┐ │
│ │设备1-8  │ │伺服电机 │ │传感器组 │ │  高精度定位系统      │ │
│ │C++接口  │ │控制系统 │ │电容/光栅│ │  (6DOF控制)        │ │
│ └─────────┘ └─────────┘ └─────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔧 核心模块设计

### 2.1 流程引擎平台内核

#### 2.1.1 核心职责
- 工单流程编排和执行
- 任务拆解和工序流程建模
- 流程状态管理和监控
- 异常流程处理和恢复

#### 2.1.2 技术架构
```java
@Component
public class ProcessEngineCore {
    
    @Autowired
    private WorkflowExecutor workflowExecutor;
    
    @Autowired
    private TaskScheduler taskScheduler;
    
    @Autowired
    private ProcessStateManager stateManager;
    
    // 流程定义和执行
    public ProcessInstance executeWorkflow(WorkOrder workOrder) {
        // 1. 解析工单要求
        ProcessDefinition definition = parseWorkOrder(workOrder);
        
        // 2. 创建流程实例
        ProcessInstance instance = createProcessInstance(definition);
        
        // 3. 启动流程执行
        return workflowExecutor.execute(instance);
    }
    
    // 高精度装配流程专用
    public boolean executePrecisionAssembly(TargetAssemblyTask task) {
        // 集成高精度定位控制
        return precisionPositionController.executeAssembly(task);
    }
}
```

#### 2.1.3 流程定义示例
```xml
<process id="targetAssemblyProcess" name="靶丸精密装配流程">
    <startEvent id="start"/>
    
    <serviceTask id="roughPositioning" name="粗定位">
        <implementation>com.system.positioning.RoughPositioningService</implementation>
    </serviceTask>
    
    <serviceTask id="precisionPositioning" name="精密定位">
        <implementation>com.system.positioning.PrecisionPositioningService</implementation>
        <property name="accuracy" value="±10μm"/>
        <property name="timeout" value="30s"/>
    </serviceTask>
    
    <userTask id="humanConfirmation" name="人工确认">
        <assignment>
            <candidateGroups>operators</candidateGroups>
        </assignment>
    </userTask>
    
    <serviceTask id="qualityCheck" name="质量检测">
        <implementation>com.system.detection.QualityCheckService</implementation>
    </serviceTask>
    
    <endEvent id="end"/>
    
    <sequenceFlow sourceRef="start" targetRef="roughPositioning"/>
    <sequenceFlow sourceRef="roughPositioning" targetRef="precisionPositioning"/>
    <sequenceFlow sourceRef="precisionPositioning" targetRef="humanConfirmation"/>
    <sequenceFlow sourceRef="humanConfirmation" targetRef="qualityCheck"/>
    <sequenceFlow sourceRef="qualityCheck" targetRef="end"/>
</process>
```

### 2.2 高精度定位控制模块

#### 2.2.1 系统架构
```cpp
// 高精度定位控制主类
class PrecisionPositionController {
private:
    std::unique_ptr<MotionController> motionController;
    std::unique_ptr<SensorFusion> sensorFusion;
    std::unique_ptr<DOF6Coordinator> dof6Coordinator;
    
public:
    // 6DOF协调控制
    class DOF6Coordinator {
    private:
        ServoMotorController servoMotors[3];    // X,Y,Z轴
        CapacitiveSensor capacitiveSensors[6];  // 6个电容传感器
        OpticalEncoder opticalEncoders[3];      // 光栅尺
        PIDController pidControllers[6];        // 6DOF PID控制器
        
    public:
        // 执行精密定位
        bool executePrecisionPositioning(const TargetPosition& target) {
            // 1. 粗定位阶段（伺服电机+光栅尺）
            if (!executeCoarsePositioning(target)) {
                return false;
            }
            
            // 2. 精定位阶段（电容传感器+微调）
            if (!executeFinePositioning(target)) {
                return false;
            }
            
            // 3. 6DOF协调控制
            return execute6DOFControl(target);
        }
        
    private:
        // 粗定位：±5μm精度
        bool executeCoarsePositioning(const TargetPosition& target) {
            for (int axis = 0; axis < 3; axis++) {
                Position current = opticalEncoders[axis].getCurrentPosition();
                double error = target.position[axis] - current.value;
                
                if (std::abs(error) > 5e-6) { // 5μm
                    servoMotors[axis].moveTo(target.position[axis]);
                }
            }
            return verifyCoarseAccuracy(target, 5e-6);
        }
        
        // 精定位：±1μm精度
        bool executeFinePositioning(const TargetPosition& target) {
            // 使用电容传感器进行精密测量和微调
            SensorData sensorData = sensorFusion->fuseSensorData();
            
            for (int i = 0; i < 6; i++) {
                double error = calculatePositionError(sensorData, target, i);
                double correction = pidControllers[i].calculate(error);
                applyMicroAdjustment(i, correction);
            }
            
            return verifyFineAccuracy(target, 1e-6);
        }
        
        // 6DOF协调控制
        bool execute6DOFControl(const TargetPosition& target) {
            // 钢梯传感器阵列数据处理
            Matrix6x6 jacobian = calculateJacobian();
            Vector6 errors = calculateAllAxisErrors(target);
            Vector6 corrections = jacobian.inverse() * errors;
            
            // 同步执行所有轴的修正
            return applySynchronizedCorrections(corrections);
        }
    };
};
```

#### 2.2.2 传感器数据融合
```cpp
class SensorFusion {
private:
    KalmanFilter kalmanFilter;
    
public:
    // 多传感器数据融合
    SensorData fuseSensorData() {
        // 1. 获取光栅尺数据（粗定位参考）
        Vector3 opticalData = getOpticalEncoderData();
        
        // 2. 获取电容传感器数据（精定位参考）
        Vector6 capacitiveData = getCapacitiveSensorData();
        
        // 3. 获取钢梯传感器数据（角度参考）
        Vector6 steelLadderData = getSteelLadderSensorData();
        
        // 4. 卡尔曼滤波融合
        return kalmanFilter.fuse(opticalData, capacitiveData, steelLadderData);
    }
    
private:
    Vector6 getCapacitiveSensorData() {
        Vector6 data;
        for (int i = 0; i < 6; i++) {
            data[i] = capacitiveSensors[i].readPosition();
            // 30nm分辨率，温度补偿
            data[i] = temperatureCompensation(data[i]);
        }
        return data;
    }
};
```

### 2.3 设备组调度模块

#### 2.3.1 设备抽象层
```java
// 设备统一接口
public interface DeviceController {
    DeviceStatus getStatus();
    boolean executeCommand(DeviceCommand command);
    void emergencyStop();
    DeviceCapability getCapability();
}

// 8套设备的统一管理
@Service
public class DeviceGroupScheduler {
    
    private final Map<String, DeviceController> devices = new HashMap<>();
    private final PriorityQueue<Task> taskQueue = new PriorityQueue<>();
    
    // 设备组调度策略
    public void scheduleTask(Task task) {
        // 1. 分析任务需求
        DeviceRequirement requirement = analyzeTaskRequirement(task);
        
        // 2. 选择最优设备
        DeviceController selectedDevice = selectOptimalDevice(requirement);
        
        // 3. 检查设备状态
        if (selectedDevice.getStatus() == DeviceStatus.READY) {
            // 4. 分配任务
            assignTaskToDevice(task, selectedDevice);
        } else {
            // 5. 加入等待队列
            taskQueue.offer(task);
        }
    }
    
    // 高精度装配任务的特殊调度
    public void schedulePrecisionAssemblyTask(PrecisionAssemblyTask task) {
        // 需要高精度定位系统配合
        if (precisionPositionController.isAvailable()) {
            // 协调设备和定位系统
            coordinateDeviceAndPositioning(task);
        }
    }
}
```

### 2.4 检测算法插件管理器

#### 2.4.1 插件架构设计
```java
// 检测算法插件接口
public interface DetectionPlugin {
    String getPluginId();
    String getVersion();
    DetectionCapability getCapability();
    DetectionResult detect(ImageData imageData, DetectionParameters params);
    boolean initialize(PluginConfig config);
    void cleanup();
}

// 插件管理器
@Component
public class PluginManager {
    
    private final Map<String, DetectionPlugin> plugins = new ConcurrentHashMap<>();
    
    // 动态加载插件
    public void loadPlugin(String pluginPath) {
        try {
            DetectionPlugin plugin = PluginLoader.load(pluginPath);
            plugins.put(plugin.getPluginId(), plugin);
            logger.info("Plugin loaded: {}", plugin.getPluginId());
        } catch (Exception e) {
            logger.error("Failed to load plugin: {}", pluginPath, e);
        }
    }
    
    // 执行检测
    public DetectionResult executeDetection(String pluginId, ImageData imageData) {
        DetectionPlugin plugin = plugins.get(pluginId);
        if (plugin == null) {
            throw new PluginNotFoundException("Plugin not found: " + pluginId);
        }
        
        // 针对靶丸检测的特殊参数
        DetectionParameters params = createTargetDetectionParams();
        return plugin.detect(imageData, params);
    }
    
    private DetectionParameters createTargetDetectionParams() {
        return DetectionParameters.builder()
            .accuracy(10e-6)  // ±10μm精度要求
            .algorithm("subpixel_edge_detection")
            .materialType("organic_plastic")
            .targetSize("2-3mm")
            .build();
    }
}
```

### 2.5 人机交互模块

#### 2.5.1 Qt客户端架构
```cpp
// 主界面控制器
class MainController : public QObject {
    Q_OBJECT
    
private:
    std::unique_ptr<ProcessMonitor> processMonitor;
    std::unique_ptr<DeviceStatusWidget> deviceStatus;
    std::unique_ptr<PrecisionControlWidget> precisionControl;
    
public slots:
    // 处理精密定位确认
    void onPrecisionPositioningConfirm() {
        PrecisionStatus status = precisionControl->getCurrentStatus();
        if (status.accuracy <= 10e-6) {  // ±10μm
            emit precisionPositioningConfirmed(status);
        } else {
            showAccuracyWarning(status.accuracy);
        }
    }
    
    // 处理异常情况
    void onEmergencyStop() {
        // 紧急停止所有运动
        precisionPositionController->emergencyStop();
        emit emergencyStopTriggered();
    }
    
signals:
    void precisionPositioningConfirmed(const PrecisionStatus& status);
    void emergencyStopTriggered();
};

// 精密控制界面
class PrecisionControlWidget : public QWidget {
    Q_OBJECT
    
private:
    QLabel* accuracyDisplay;
    QProgressBar* positioningProgress;
    QPushButton* confirmButton;
    QPushButton* adjustButton;
    
public:
    void updateAccuracyDisplay(double currentAccuracy) {
        QString text = QString("当前精度: ±%1μm").arg(currentAccuracy * 1e6, 0, 'f', 2);
        accuracyDisplay->setText(text);
        
        // 精度指示颜色
        if (currentAccuracy <= 10e-6) {
            accuracyDisplay->setStyleSheet("color: green; font-weight: bold;");
            confirmButton->setEnabled(true);
        } else {
            accuracyDisplay->setStyleSheet("color: red; font-weight: bold;");
            confirmButton->setEnabled(false);
        }
    }
};
```

---

## 🔌 技术选型和接口规范

### 3.1 后端架构（SpringBoot 3.x）

#### 3.1.1 核心依赖配置
```xml
<!-- pom.xml -->
<dependencies>
    <!-- SpringBoot核心 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>3.2.0</version>
    </dependency>
    
    <!-- 实时通信 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>
    
    <!-- 数据库支持 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    
    <!-- 流程引擎 -->
    <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter</artifactId>
        <version>7.0.0</version>
    </dependency>
    
    <!-- MQTT支持（数字孪生） -->
    <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mqtt</artifactId>
    </dependency>
</dependencies>
```

#### 3.1.2 应用配置
```yaml
# application.yml
server:
  port: 8080

spring:
  application:
    name: detection-control-system
  
  datasource:
    url: **************************************************
    username: ${DB_USERNAME:admin}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true

# 自定义配置
system:
  precision:
    target-accuracy: 10e-6  # ±10μm
    positioning-timeout: 30000  # 30秒
  
  devices:
    count: 8
    communication-protocol: "TCP"
    heartbeat-interval: 5000
  
  mes:
    endpoint: "http://mes-system:8080/api"
    timeout: 10000
  
  digital-twin:
    mqtt-broker: "tcp://dt-platform:1883"
    topic-prefix: "detection-control"

### 3.2 前端架构（Qt6/Qt5.15 C++）

#### 3.2.1 项目结构
```
DetectionControlClient/
├── src/
│   ├── main.cpp
│   ├── controllers/
│   │   ├── MainController.h/cpp
│   │   ├── PrecisionController.h/cpp
│   │   └── DeviceController.h/cpp
│   ├── widgets/
│   │   ├── PrecisionControlWidget.h/cpp
│   │   ├── ProcessMonitorWidget.h/cpp
│   │   └── DeviceStatusWidget.h/cpp
│   ├── models/
│   │   ├── ProcessModel.h/cpp
│   │   └── DeviceModel.h/cpp
│   ├── network/
│   │   ├── ApiClient.h/cpp
│   │   └── WebSocketClient.h/cpp
│   └── utils/
│       ├── Logger.h/cpp
│       └── ConfigManager.h/cpp
├── resources/
│   ├── icons/
│   ├── qml/
│   └── translations/
└── CMakeLists.txt
```

#### 3.2.2 CMake配置
```cmake
# CMakeLists.txt
cmake_minimum_required(VERSION 3.16)
project(DetectionControlClient)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network WebSockets)

set(SOURCES
    src/main.cpp
    src/controllers/MainController.cpp
    src/controllers/PrecisionController.cpp
    src/widgets/PrecisionControlWidget.cpp
    src/widgets/ProcessMonitorWidget.cpp
    src/network/ApiClient.cpp
    src/network/WebSocketClient.cpp
)

qt6_add_executable(DetectionControlClient ${SOURCES})
qt6_add_resources(DetectionControlClient "resources" PREFIX "/")

target_link_libraries(DetectionControlClient
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
    Qt6::WebSockets
)
```

### 3.3 数据库设计

#### 3.3.1 核心数据表
```sql
-- 工单表
CREATE TABLE work_orders (
    id BIGSERIAL PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    product_type VARCHAR(100) NOT NULL,
    quantity INTEGER NOT NULL,
    precision_requirement DECIMAL(10,9) DEFAULT 10e-6, -- ±10μm
    status VARCHAR(20) DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 流程实例表
CREATE TABLE process_instances (
    id BIGSERIAL PRIMARY KEY,
    work_order_id BIGINT REFERENCES work_orders(id),
    process_definition_key VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'RUNNING',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    current_activity VARCHAR(100)
);

-- 精密定位记录表
CREATE TABLE precision_positioning_records (
    id BIGSERIAL PRIMARY KEY,
    process_instance_id BIGINT REFERENCES process_instances(id),
    target_x DECIMAL(15,9) NOT NULL,
    target_y DECIMAL(15,9) NOT NULL,
    target_z DECIMAL(15,9) NOT NULL,
    actual_x DECIMAL(15,9) NOT NULL,
    actual_y DECIMAL(15,9) NOT NULL,
    actual_z DECIMAL(15,9) NOT NULL,
    accuracy_achieved DECIMAL(10,9) NOT NULL,
    positioning_time INTEGER NOT NULL, -- 毫秒
    sensor_data JSONB, -- 传感器原始数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设备状态表
CREATE TABLE device_status (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    device_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    current_task_id BIGINT,
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT,
    performance_metrics JSONB
);

-- 质量检测记录表
CREATE TABLE quality_inspection_records (
    id BIGSERIAL PRIMARY KEY,
    process_instance_id BIGINT REFERENCES process_instances(id),
    inspection_type VARCHAR(50) NOT NULL,
    result VARCHAR(20) NOT NULL, -- PASS/FAIL
    measured_values JSONB,
    inspector_id VARCHAR(50),
    inspection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remarks TEXT
);
```

### 3.4 通信协议设计

#### 3.4.1 EtherCAT实时通信
```cpp
// EtherCAT通信管理器
class EtherCATManager {
private:
    ec_master_t* master;
    ec_domain_t* domain;

public:
    // 初始化EtherCAT主站
    bool initialize() {
        master = ecrt_request_master(0);
        if (!master) {
            return false;
        }

        domain = ecrt_master_create_domain(master);
        if (!domain) {
            return false;
        }

        // 配置从站
        configureSlaves();

        // 激活主站
        if (ecrt_master_activate(master)) {
            return false;
        }

        return true;
    }

    // 实时数据交换
    void cyclicTask() {
        // 接收过程数据
        ecrt_master_receive(master);
        ecrt_domain_process(domain);

        // 读取传感器数据
        readSensorData();

        // 写入控制指令
        writeControlCommands();

        // 发送过程数据
        ecrt_domain_queue(domain);
        ecrt_master_send(master);
    }

private:
    void configureSlaves() {
        // 配置伺服驱动器
        for (int i = 0; i < 3; i++) {
            ec_slave_config_t* servo = ecrt_master_slave_config(
                master, 0, i, SERVO_VENDOR_ID, SERVO_PRODUCT_CODE);
            // 配置PDO映射
            configureServoPDO(servo, i);
        }

        // 配置I/O模块
        ec_slave_config_t* io_module = ecrt_master_slave_config(
            master, 0, 3, IO_VENDOR_ID, IO_PRODUCT_CODE);
        configureIOPDO(io_module);
    }
};
```

#### 3.4.2 MES系统接口
```java
// MES系统接口服务
@Service
public class MESIntegrationService {

    @Value("${system.mes.endpoint}")
    private String mesEndpoint;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    // 接收工单
    public WorkOrder receiveWorkOrder(String orderNumber) {
        String url = mesEndpoint + "/workorders/" + orderNumber;

        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            return objectMapper.readValue(response.getBody(), WorkOrder.class);
        } catch (Exception e) {
            logger.error("Failed to receive work order: {}", orderNumber, e);
            throw new MESIntegrationException("Failed to receive work order", e);
        }
    }

    // 回传执行状态
    public void reportExecutionStatus(String orderNumber, ExecutionStatus status) {
        String url = mesEndpoint + "/workorders/" + orderNumber + "/status";

        StatusReport report = StatusReport.builder()
            .orderNumber(orderNumber)
            .status(status.getStatus())
            .completedQuantity(status.getCompletedQuantity())
            .qualityData(status.getQualityData())
            .timestamp(Instant.now())
            .build();

        try {
            restTemplate.postForEntity(url, report, Void.class);
        } catch (Exception e) {
            logger.error("Failed to report status for order: {}", orderNumber, e);
        }
    }
}
```

#### 3.4.3 数字孪生MQTT接口
```java
// 数字孪生数据发布服务
@Service
public class DigitalTwinPublisher {

    @Autowired
    private MqttTemplate mqttTemplate;

    @Value("${system.digital-twin.topic-prefix}")
    private String topicPrefix;

    // 发布精密定位数据
    public void publishPositioningData(PrecisionPositioningData data) {
        String topic = topicPrefix + "/positioning/realtime";

        PositioningMessage message = PositioningMessage.builder()
            .timestamp(Instant.now())
            .deviceId("precision-positioning-system")
            .position(data.getCurrentPosition())
            .accuracy(data.getAchievedAccuracy())
            .status(data.getStatus())
            .build();

        try {
            String payload = objectMapper.writeValueAsString(message);
            mqttTemplate.convertAndSend(topic, payload);
        } catch (Exception e) {
            logger.error("Failed to publish positioning data", e);
        }
    }

    // 发布设备状态
    public void publishDeviceStatus(String deviceId, DeviceStatus status) {
        String topic = topicPrefix + "/devices/" + deviceId + "/status";

        DeviceStatusMessage message = DeviceStatusMessage.builder()
            .timestamp(Instant.now())
            .deviceId(deviceId)
            .status(status.getStatus())
            .currentTask(status.getCurrentTask())
            .performance(status.getPerformanceMetrics())
            .build();

        try {
            String payload = objectMapper.writeValueAsString(message);
            mqttTemplate.convertAndSend(topic, payload);
        } catch (Exception e) {
            logger.error("Failed to publish device status for: {}", deviceId, e);
        }
    }
}
```

---

## 🎛️ 高精度控制系统集成

### 4.1 BECKHOFF TwinCAT实时控制架构

#### 4.1.1 TwinCAT项目结构
```
TwinCAT_Project/
├── PLC/
│   ├── POUs/
│   │   ├── MAIN.ST                    // 主程序
│   │   ├── PrecisionControl.ST        // 精密控制
│   │   ├── MotionControl.ST           // 运动控制
│   │   └── SafetyControl.ST           // 安全控制
│   ├── DUTs/
│   │   ├── ST_Position.ST             // 位置数据结构
│   │   ├── ST_SensorData.ST           // 传感器数据结构
│   │   └── ST_ControlParams.ST        // 控制参数结构
│   └── GVLs/
│       ├── GVL_Motion.ST              // 运动全局变量
│       └── GVL_Sensors.ST             // 传感器全局变量
├── Motion/
│   ├── Axes/
│   │   ├── Axis_X.xml                 // X轴配置
│   │   ├── Axis_Y.xml                 // Y轴配置
│   │   └── Axis_Z.xml                 // Z轴配置
│   └── Groups/
│       └── PrecisionGroup.xml         // 精密控制组
└── IO/
    ├── EtherCAT_Master.xml
    ├── Servo_Drives.xml
    └── Sensor_Modules.xml
```

#### 4.1.2 精密控制程序
```pascal
// PrecisionControl.ST
PROGRAM PrecisionControl
VAR
    // 目标位置
    targetPosition : ST_Position;
    currentPosition : ST_Position;

    // 控制参数
    precisionTolerance : LREAL := 10E-6; // ±10μm
    coarseTolerance : LREAL := 5E-6;     // ±5μm粗定位
    fineTolerance : LREAL := 1E-6;       // ±1μm精定位

    // 状态机
    state : E_PrecisionControlState := E_PrecisionControlState.IDLE;

    // 运动控制
    mcMoveAbsolute_X : MC_MoveAbsolute;
    mcMoveAbsolute_Y : MC_MoveAbsolute;
    mcMoveAbsolute_Z : MC_MoveAbsolute;

    // 传感器数据
    sensorData : ST_SensorData;

    // 控制结果
    positioningResult : ST_PositioningResult;
END_VAR

// 主控制逻辑
CASE state OF
    E_PrecisionControlState.IDLE:
        IF startPositioning THEN
            state := E_PrecisionControlState.COARSE_POSITIONING;
        END_IF

    E_PrecisionControlState.COARSE_POSITIONING:
        // 粗定位：使用伺服电机+光栅尺
        ExecuteCoarsePositioning();
        IF CoarsePositioningComplete() THEN
            state := E_PrecisionControlState.FINE_POSITIONING;
        END_IF

    E_PrecisionControlState.FINE_POSITIONING:
        // 精定位：使用电容传感器+微调
        ExecuteFinePositioning();
        IF FinePositioningComplete() THEN
            state := E_PrecisionControlState.VERIFICATION;
        END_IF

    E_PrecisionControlState.VERIFICATION:
        // 精度验证
        IF VerifyAccuracy() THEN
            state := E_PrecisionControlState.COMPLETE;
            positioningResult.success := TRUE;
            positioningResult.achievedAccuracy := CalculateActualAccuracy();
        ELSE
            state := E_PrecisionControlState.FINE_POSITIONING; // 重试
        END_IF

    E_PrecisionControlState.COMPLETE:
        // 定位完成，等待下一个任务
        IF resetCommand THEN
            state := E_PrecisionControlState.IDLE;
        END_IF
END_CASE
```

### 4.2 多传感器数据融合算法

#### 4.2.1 卡尔曼滤波实现
```cpp
class MultiSensorFusion {
private:
    // 状态向量 [x, y, z, rx, ry, rz, vx, vy, vz, wx, wy, wz]
    Eigen::VectorXd state;
    Eigen::MatrixXd P; // 协方差矩阵
    Eigen::MatrixXd Q; // 过程噪声
    Eigen::MatrixXd R; // 测量噪声

public:
    // 初始化滤波器
    void initialize() {
        state = Eigen::VectorXd::Zero(12);
        P = Eigen::MatrixXd::Identity(12, 12) * 0.1;

        // 过程噪声矩阵
        Q = Eigen::MatrixXd::Identity(12, 12) * 1e-8;

        // 测量噪声矩阵
        R = Eigen::MatrixXd::Identity(9, 9);
        R.diagonal() << 1e-7, 1e-7, 1e-7,  // 光栅尺噪声
                       3e-8, 3e-8, 3e-8,  // 电容传感器噪声
                       1e-6, 1e-6, 1e-6;  // 角度传感器噪声
    }

    // 预测步骤
    void predict(double dt) {
        // 状态转移矩阵
        Eigen::MatrixXd F = Eigen::MatrixXd::Identity(12, 12);
        F.block<6,6>(0,6) = Eigen::MatrixXd::Identity(6,6) * dt;

        // 预测状态
        state = F * state;
        P = F * P * F.transpose() + Q;
    }

    // 更新步骤
    void update(const SensorMeasurement& measurement) {
        // 测量矩阵
        Eigen::MatrixXd H = Eigen::MatrixXd::Zero(9, 12);
        H.block<9,9>(0,0) = Eigen::MatrixXd::Identity(9,9);

        // 测量向量
        Eigen::VectorXd z(9);
        z << measurement.opticalEncoder.x,
             measurement.opticalEncoder.y,
             measurement.opticalEncoder.z,
             measurement.capacitiveSensor.x,
             measurement.capacitiveSensor.y,
             measurement.capacitiveSensor.z,
             measurement.angleSensor.rx,
             measurement.angleSensor.ry,
             measurement.angleSensor.rz;

        // 卡尔曼增益
        Eigen::MatrixXd S = H * P * H.transpose() + R;
        Eigen::MatrixXd K = P * H.transpose() * S.inverse();

        // 更新状态
        Eigen::VectorXd y = z - H * state;
        state = state + K * y;
        P = (Eigen::MatrixXd::Identity(12, 12) - K * H) * P;
    }

    // 获取融合后的位置
    Position getFusedPosition() {
        Position pos;
        pos.x = state(0);
        pos.y = state(1);
        pos.z = state(2);
        pos.rx = state(3);
        pos.ry = state(4);
        pos.rz = state(5);
        return pos;
    }
};
```

---

## 📊 数据流和控制流设计

### 5.1 完整业务流程

#### 5.1.1 从MES到装配完成的数据流

```
┌─────────────────┐
│  MES系统下发工单  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  工单解析和验证   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  创建流程实例    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  设备可用性检查   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  启动装配流程    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   粗定位阶段     │───▶│  精密定位阶段    │───▶│  6DOF协调控制   │
│  (伺服+光栅尺)   │    │ (电容传感器)     │    │  (钢梯传感器)   │
└─────────────────┘    └─────────────────┘    └─────────┬───────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │    精度验证      │
                                              └─────────┬───────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │ 精度是否达标？   │
                                              └─────────┬───────┘
                                                        │
                                    ┌───────────────────┼───────────────────┐
                                    │ 否                │                   │ 是
                                    ▼                   ▼                   ▼
                          ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
                          │   微调重试      │  │                 │  │    人工确认      │
                          └─────────┬───────┘  │                 │  └─────────┬───────┘
                                    │          │                 │            │
                                    └──────────┘                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │    质量检测      │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │    数据记录      │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │  状态回传MES    │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │ 数字孪生更新     │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │   流程完成       │
                                                                 │  └─────────────────┘
                                                                 │
                                                                 └─ 回到6DOF协调控制 ──┘
```

#### 5.1.2 实时数据采集流程
```java
@Component
public class RealTimeDataCollector {

    @Autowired
    private SensorDataService sensorDataService;

    @Autowired
    private DigitalTwinPublisher digitalTwinPublisher;

    // 实时数据采集任务
    @Scheduled(fixedRate = 100) // 10Hz采集频率
    public void collectRealTimeData() {
        try {
            // 1. 采集传感器数据
            SensorData sensorData = collectSensorData();

            // 2. 数据预处理和滤波
            ProcessedData processedData = preprocessData(sensorData);

            // 3. 存储到时序数据库
            sensorDataService.store(processedData);

            // 4. 发布到数字孪生平台
            digitalTwinPublisher.publishSensorData(processedData);

            // 5. 实时精度监控
            monitorAccuracy(processedData);

        } catch (Exception e) {
            logger.error("Real-time data collection failed", e);
        }
    }

    private SensorData collectSensorData() {
        return SensorData.builder()
            .timestamp(Instant.now())
            .opticalEncoderData(readOpticalEncoders())
            .capacitiveSensorData(readCapacitiveSensors())
            .angleSensorData(readAngleSensors())
            .temperatureData(readTemperatureSensors())
            .build();
    }

    private void monitorAccuracy(ProcessedData data) {
        double currentAccuracy = calculateCurrentAccuracy(data);

        if (currentAccuracy > PRECISION_THRESHOLD) {
            // 触发精度告警
            alertService.sendAccuracyAlert(currentAccuracy);

            // 自动调整控制参数
            precisionController.adjustControlParameters(currentAccuracy);
        }
    }
}
```

### 5.2 异常处理和容错机制

#### 5.2.1 异常处理架构
```java
// 全局异常处理器
@ControllerAdvice
public class GlobalExceptionHandler {

    // 精度异常处理
    @ExceptionHandler(PrecisionException.class)
    public ResponseEntity<ErrorResponse> handlePrecisionException(PrecisionException e) {
        logger.error("Precision error occurred", e);

        // 触发精度恢复流程
        precisionRecoveryService.startRecovery(e.getContext());

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ErrorResponse.builder()
                .code("PRECISION_ERROR")
                .message(e.getMessage())
                .timestamp(Instant.now())
                .build());
    }

    // 设备通信异常处理
    @ExceptionHandler(DeviceCommunicationException.class)
    public ResponseEntity<ErrorResponse> handleDeviceCommunicationException(
            DeviceCommunicationException e) {

        // 尝试重新建立连接
        deviceConnectionManager.reconnect(e.getDeviceId());

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
            .body(ErrorResponse.builder()
                .code("DEVICE_COMMUNICATION_ERROR")
                .message("Device communication failed: " + e.getDeviceId())
                .timestamp(Instant.now())
                .build());
    }
}

// 精度恢复服务
@Service
public class PrecisionRecoveryService {

    public void startRecovery(PrecisionContext context) {
        logger.info("Starting precision recovery for context: {}", context);

        // 1. 停止当前运动
        motionController.emergencyStop();

        // 2. 重新校准传感器
        sensorCalibrationService.recalibrate();

        // 3. 重新执行定位
        precisionPositionController.retryPositioning(context.getTargetPosition());

        // 4. 验证恢复结果
        if (!verifyRecovery(context)) {
            // 如果恢复失败，转入人工干预模式
            humanInterventionService.requestIntervention(context);
        }
    }
}
```

#### 5.2.2 容错机制设计
```cpp
// 容错控制器
class FaultTolerantController {
private:
    std::vector<SensorChannel> sensorChannels;
    RedundancyManager redundancyManager;

public:
    // 传感器冗余处理
    Position getReliablePosition() {
        std::vector<Position> positions;

        // 从多个传感器获取位置数据
        for (auto& channel : sensorChannels) {
            if (channel.isHealthy()) {
                positions.push_back(channel.getPosition());
            }
        }

        // 如果传感器数量不足，触发告警
        if (positions.size() < MIN_SENSOR_COUNT) {
            triggerSensorFaultAlert();
        }

        // 使用中位数滤波获取可靠位置
        return calculateMedianPosition(positions);
    }

    // 控制系统冗余
    bool executeControlCommand(ControlCommand command) {
        // 主控制器执行
        bool primaryResult = primaryController.execute(command);

        if (!primaryResult) {
            logger.warn("Primary controller failed, switching to backup");

            // 切换到备用控制器
            bool backupResult = backupController.execute(command);

            if (!backupResult) {
                // 两个控制器都失败，进入安全模式
                enterSafeMode();
                return false;
            }
        }

        return true;
    }

private:
    void enterSafeMode() {
        // 1. 停止所有运动
        emergencyStop();

        // 2. 保存当前状态
        saveCurrentState();

        // 3. 通知操作员
        notifyOperator("System entered safe mode due to control failure");

        // 4. 等待人工干预
        waitForManualIntervention();
    }
};
```

---

## 🔧 系统部署和运维

### 6.1 Docker容器化部署

#### 6.1.1 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 主应用服务
  detection-control-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped

  # 数据库服务
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: detection_control
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  # 缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # 时序数据库（传感器数据）
  influxdb:
    image: influxdb:2.7
    ports:
      - "8086:8086"
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password
      DOCKER_INFLUXDB_INIT_ORG: detection-control
      DOCKER_INFLUXDB_INIT_BUCKET: sensor-data
    volumes:
      - influxdb_data:/var/lib/influxdb2

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus

  # 可视化服务
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources

volumes:
  postgres_data:
  redis_data:
  influxdb_data:
  prometheus_data:
  grafana_data:
```

### 6.2 监控和告警

#### 6.2.1 系统监控指标
```java
// 自定义监控指标
@Component
public class SystemMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter precisionErrorCounter;
    private final Timer positioningTimer;
    private final Gauge currentAccuracyGauge;

    public SystemMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 精度错误计数器
        this.precisionErrorCounter = Counter.builder("precision.errors.total")
            .description("Total number of precision errors")
            .register(meterRegistry);

        // 定位时间计时器
        this.positioningTimer = Timer.builder("positioning.duration")
            .description("Time taken for precision positioning")
            .register(meterRegistry);

        // 当前精度指标
        this.currentAccuracyGauge = Gauge.builder("positioning.accuracy.current")
            .description("Current positioning accuracy in micrometers")
            .register(meterRegistry, this, SystemMetrics::getCurrentAccuracy);
    }

    public void recordPrecisionError() {
        precisionErrorCounter.increment();
    }

    public void recordPositioningTime(Duration duration) {
        positioningTimer.record(duration);
    }

    private double getCurrentAccuracy() {
        // 从精度控制器获取当前精度
        return precisionController.getCurrentAccuracy() * 1e6; // 转换为微米
    }
}
```

---

## 📋 总结

本软件架构设计文档提供了一个完整的检测控制程序系统架构，具有以下特点：

### 🎯 核心能力
- **高精度定位：** 支持±10μm靶丸定位精度要求
- **实时控制：** 基于BECKHOFF TwinCAT的实时控制系统
- **多传感器融合：** 光栅尺+电容传感器+角度传感器的数据融合
- **6DOF协调控制：** 完整的空间位置和姿态控制

### 🏗️ 架构优势
- **模块化设计：** 清晰的四层架构，便于维护和扩展
- **技术成熟：** 基于SpringBoot、Qt、TwinCAT等成熟技术栈
- **容错能力：** 完善的异常处理和容错机制
- **可扩展性：** 支持未来功能扩展和精度升级

### 🔧 实施建议
1. **分阶段开发：** 先实现核心功能，再逐步完善
2. **充分测试：** 特别是精度控制和实时性能测试
3. **专家支持：** 与微电子研究所保持技术合作
4. **持续优化：** 根据实际运行情况持续优化算法和参数

这个架构设计为项目的成功实施提供了坚实的技术基础。
```
