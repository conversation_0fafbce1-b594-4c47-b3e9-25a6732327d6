# 检测控制程序系统流程图集合

## 📋 说明

本文档包含检测控制程序系统的各种流程图，提供多种格式以确保在不同环境下都能正常查看。

---

## 🔄 主要业务流程图

### 1. 从MES到装配完成的完整数据流

#### Mermaid格式（需要支持Mermaid的查看器）
```mermaid
graph TD
    A[MES系统下发工单] --> B[工单解析和验证]
    B --> C[创建流程实例]
    C --> D[设备可用性检查]
    D --> E[启动装配流程]

    E --> F[粗定位阶段]
    F --> G[精密定位阶段]
    G --> H[6DOF协调控制]
    H --> I[精度验证]

    I --> J{精度是否达标?}
    J -->|否| K[微调重试]
    K --> H
    J -->|是| L[人工确认]

    L --> M[质量检测]
    M --> N[数据记录]
    N --> O[状态回传MES]
    O --> P[数字孪生更新]
    P --> Q[流程完成]
```

#### ASCII艺术格式（通用兼容）
```
┌─────────────────┐
│  MES系统下发工单  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  工单解析和验证   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  创建流程实例    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  设备可用性检查   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  启动装配流程    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ 高精度伺服定位   │───▶│  精密定位阶段    │───▶│  6DOF协调控制   │
│ (23bit编码器)    │    │ (电容传感器)     │    │  (钢梯传感器)   │
└─────────────────┘    └─────────────────┘    └─────────┬───────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │    精度验证      │
                                              └─────────┬───────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │ 精度是否达标？   │
                                              └─────────┬───────┘
                                                        │
                                    ┌───────────────────┼───────────────────┐
                                    │ 否                │                   │ 是
                                    ▼                   ▼                   ▼
                          ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
                          │   微调重试      │  │                 │  │    人工确认      │
                          └─────────┬───────┘  │                 │  └─────────┬───────┘
                                    │          │                 │            │
                                    └──────────┘                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │    质量检测      │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │    数据记录      │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │  状态回传MES    │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │ 数字孪生更新     │
                                                                 │  └─────────┬───────┘
                                                                 │            │
                                                                 │            ▼
                                                                 │  ┌─────────────────┐
                                                                 │  │   流程完成       │
                                                                 │  └─────────────────┘
                                                                 │
                                                                 └─ 回到6DOF协调控制 ──┘
```

### 2. 高精度定位控制流程

#### Mermaid格式
```mermaid
graph TD
    A[接收定位任务] --> B[系统初始化检查]
    B --> C[传感器校准]
    C --> D[粗定位启动]
    
    D --> E[伺服电机运动]
    E --> F[光栅尺位置反馈]
    F --> G{粗定位精度检查}
    G -->|精度>5μm| E
    G -->|精度≤5μm| H[精定位启动]
    
    H --> I[电容传感器测量]
    I --> J[压电微调执行]
    J --> K[6DOF协调控制]
    K --> L[钢梯传感器反馈]
    L --> M{最终精度检查}
    
    M -->|精度>10μm| N[参数调整]
    N --> I
    M -->|精度≤10μm| O[定位成功]
    O --> P[位置锁定]
    P --> Q[状态报告]
```

#### ASCII艺术格式
```
┌─────────────────┐
│  接收定位任务    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 系统初始化检查   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│   传感器校准     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│   粗定位启动     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  伺服电机运动    │───▶│23bit编码器反馈   │───▶│ 伺服精度检查     │
└─────────────────┘    └─────────────────┘    └─────────┬───────┘
          ▲                                              │
          │                                              ▼
          │                                    ┌─────────────────┐
          │                                    │ 精度>2μm？      │
          │                                    └─────────┬───────┘
          │                                              │
          └──────────────────────────────────────────────┤
                                                         │ 精度≤2μm
                                                         ▼
                                               ┌─────────────────┐
                                               │  精定位启动      │
                                               └─────────┬───────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ 电容传感器测量   │───▶│ 压电微调执行     │───▶│ 6DOF协调控制    │
└─────────────────┘    └─────────────────┘    └─────────┬───────┘
          ▲                                              │
          │                                              ▼
          │                                    ┌─────────────────┐
          │                                    │ 钢梯传感器反馈   │
          │                                    └─────────┬───────┘
          │                                              │
          │                                              ▼
          │                                    ┌─────────────────┐
          │                                    │ 最终精度检查     │
          │                                    └─────────┬───────┘
          │                                              │
          │                                              ▼
          │                                    ┌─────────────────┐
          │                                    │ 精度>10μm？     │
          │                                    └─────────┬───────┘
          │                                              │
          │                                              ├─ 精度>10μm
          │                                              │
          │                                    ┌─────────────────┐
          │                                    │   参数调整       │
          │                                    └─────────┬───────┘
          │                                              │
          └──────────────────────────────────────────────┘
                                                         │ 精度≤10μm
                                                         ▼
                                               ┌─────────────────┐
                                               │   定位成功       │
                                               └─────────┬───────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │   位置锁定       │
                                               └─────────┬───────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │   状态报告       │
                                               └─────────────────┘
```

### 3. 设备组调度流程

#### Mermaid格式
```mermaid
graph TD
    A[任务队列接收] --> B[任务优先级分析]
    B --> C[设备能力匹配]
    C --> D[设备状态检查]
    
    D --> E{设备是否可用?}
    E -->|否| F[加入等待队列]
    F --> G[监控设备状态]
    G --> D
    
    E -->|是| H[任务分配]
    H --> I[设备执行任务]
    I --> J[实时状态监控]
    J --> K{任务是否完成?}
    
    K -->|否| L{是否有异常?}
    L -->|是| M[异常处理]
    M --> N[任务重新调度]
    N --> B
    L -->|否| J
    
    K -->|是| O[任务完成确认]
    O --> P[设备状态更新]
    P --> Q[下一任务检查]
    Q --> A
```

### 4. 异常处理流程

#### Mermaid格式
```mermaid
graph TD
    A[异常检测] --> B[异常类型识别]
    B --> C{异常严重程度}
    
    C -->|轻微| D[自动恢复尝试]
    D --> E{恢复是否成功?}
    E -->|是| F[继续正常流程]
    E -->|否| G[升级异常等级]
    G --> H[人工干预请求]
    
    C -->|严重| I[紧急停止]
    I --> J[安全状态确认]
    J --> K[异常日志记录]
    K --> L[通知维护人员]
    L --> M[等待人工处理]
    
    C -->|致命| N[系统紧急关闭]
    N --> O[保存当前状态]
    O --> P[发送紧急告警]
    P --> Q[等待系统重启]
```

---

## 🛠️ 如何查看Mermaid图表

### 方法1：在线Mermaid编辑器
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 复制上面的Mermaid代码
3. 粘贴到编辑器中查看渲染结果

### 方法2：VSCode插件
1. 安装 "Mermaid Preview" 插件
2. 在VSCode中打开包含Mermaid代码的Markdown文件
3. 使用预览功能查看渲染结果

### 方法3：GitHub/GitLab
- GitHub和GitLab都原生支持Mermaid图表渲染
- 直接在仓库中查看Markdown文件即可看到渲染结果

### 方法4：Typora等Markdown编辑器
- 使用支持Mermaid的Markdown编辑器
- 如Typora、Mark Text等

---

## 📝 使用建议

1. **文档分发时：** 使用ASCII艺术格式确保兼容性
2. **技术讨论时：** 使用Mermaid格式便于修改和版本控制
3. **演示汇报时：** 使用在线编辑器生成高质量图片
4. **系统文档时：** 两种格式都保留，提供最大兼容性

这样可以确保无论在什么环境下，流程图都能正常显示和理解。
