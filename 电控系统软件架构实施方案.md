# 电控系统软件架构实施方案

## 📋 方案概述

**项目目标：** 实现±5μm靶丸高精度定位的电控软件系统（高精度伺服版本）
**技术架构：** 三层架构 + 实时控制层
**开发预算：** 18.25万（软件开发部分）
**开发周期：** 12周并行开发

---

## 🏗️ 电控软件架构层次设计

### 1.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    人机交互层 (Qt Client)                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  操作员界面      │  │  工程师界面      │  │  监控大屏        │  │
│  │  PrecisionUI    │  │  EngineerUI     │  │  MonitorUI      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────┬───────────────────┬───────────────────┬───────────┘
              │ WebSocket         │ HTTP/REST         │ TCP
              ▼                   ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                   应用逻辑层 (SpringBoot)                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  流程控制服务    │  │  设备管理服务    │  │  数据管理服务    │  │
│  │  ProcessService │  │  DeviceService  │  │  DataService    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  精度控制服务    │  │  集成接口服务    │  │  监控告警服务    │  │
│  │  PrecisionSvc   │  │  IntegrationSvc │  │  MonitorService │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────┬───────────────────┬───────────────────┬───────────┘
              │ EtherCAT          │ HTTP/MQTT         │ TCP/UDP
              ▼                   ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                  实时控制层 (TwinCAT PLC)                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  运动控制程序    │  │  传感器处理      │  │  安全监控        │  │
│  │  MotionControl  │  │  SensorHandler  │  │  SafetyMonitor  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  精密定位控制    │  │  数据采集        │  │  诊断维护        │  │
│  │  PrecisionCtrl  │  │  DataAcquisition│  │  Diagnostics    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────┬───────────────────┬───────────────────┬───────────┘
              │ EtherCAT I/O      │ Analog/Digital    │ Fieldbus
              ▼                   ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                     硬件设备层                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  伺服驱动器      │  │  传感器模块      │  │  I/O模块        │  │
│  │  Servo Drives   │  │  Sensor Modules │  │  I/O Modules    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 实时控制层（BECKHOFF TwinCAT）程序结构

#### 1.2.1 TwinCAT项目组织结构
```
TwinCAT_PrecisionPositioning/
├── SYSTEM/                          // 系统配置
│   ├── Tasks.xml                    // 任务配置
│   └── Real-time.xml               // 实时配置
├── PLC/                            // PLC程序
│   ├── POUs/                       // 程序组织单元
│   │   ├── MAIN.ST                 // 主程序
│   │   ├── MotionControl/          // 运动控制
│   │   │   ├── CoarsePositioning.ST
│   │   │   ├── FinePositioning.ST
│   │   │   └── DOF6Coordinator.ST
│   │   ├── SensorProcessing/       // 传感器处理
│   │   │   ├── OpticalEncoder.ST
│   │   │   ├── CapacitiveSensor.ST
│   │   │   └── SensorFusion.ST
│   │   ├── SafetyControl/          // 安全控制
│   │   │   ├── EmergencyStop.ST
│   │   │   └── SafetyMonitor.ST
│   │   └── Communication/          // 通信处理
│   │       ├── EtherCATHandler.ST
│   │       └── UpperLevelComm.ST
│   ├── DUTs/                       // 数据类型
│   │   ├── ST_Position.ST
│   │   ├── ST_SensorData.ST
│   │   ├── ST_ControlParams.ST
│   │   └── ST_SystemStatus.ST
│   └── GVLs/                       // 全局变量
│       ├── GVL_Motion.ST
│       ├── GVL_Sensors.ST
│       └── GVL_Communication.ST
├── Motion/                         // 运动配置
│   ├── Axes/                       // 轴配置
│   │   ├── Axis_X.xml
│   │   ├── Axis_Y.xml
│   │   └── Axis_Z.xml
│   └── Groups/
│       └── PrecisionGroup.xml
└── IO/                            // I/O配置
    ├── EtherCAT_Master.xml
    ├── Servo_Drives.xml
    └── Sensor_Modules.xml
```

#### 1.2.2 核心控制程序实现
```pascal
// MAIN.ST - 主控制程序
PROGRAM MAIN
VAR
    // 系统状态
    systemState : E_SystemState := E_SystemState.INIT;
    
    // 控制请求
    positioningRequest : ST_PositioningRequest;
    
    // 当前状态
    currentPosition : ST_Position;
    targetPosition : ST_Position;
    
    // 控制模块实例
    coarsePositioning : CoarsePositioning;
    finePositioning : FinePositioning;
    dof6Coordinator : DOF6Coordinator;
    sensorFusion : SensorFusion;
    safetyMonitor : SafetyMonitor;
    
    // 通信接口
    upperLevelComm : UpperLevelComm;
    
    // 性能监控
    cycleTime : TIME;
    maxCycleTime : TIME;
END_VAR

// 主控制状态机
CASE systemState OF
    E_SystemState.INIT:
        // 系统初始化
        IF InitializeSystem() THEN
            systemState := E_SystemState.IDLE;
        END_IF
    
    E_SystemState.IDLE:
        // 等待定位请求
        IF positioningRequest.execute THEN
            targetPosition := positioningRequest.target;
            systemState := E_SystemState.COARSE_POSITIONING;
        END_IF
    
    E_SystemState.COARSE_POSITIONING:
        // 粗定位阶段
        coarsePositioning(
            targetPos := targetPosition,
            currentPos := currentPosition,
            execute := TRUE
        );
        
        IF coarsePositioning.done THEN
            IF coarsePositioning.success THEN
                systemState := E_SystemState.FINE_POSITIONING;
            ELSE
                systemState := E_SystemState.ERROR;
            END_IF
        END_IF
    
    E_SystemState.FINE_POSITIONING:
        // 精定位阶段
        finePositioning(
            targetPos := targetPosition,
            currentPos := currentPosition,
            execute := TRUE
        );
        
        IF finePositioning.done THEN
            IF finePositioning.success THEN
                systemState := E_SystemState.DOF6_CONTROL;
            ELSE
                systemState := E_SystemState.COARSE_POSITIONING; // 重试
            END_IF
        END_IF
    
    E_SystemState.DOF6_CONTROL:
        // 6DOF协调控制
        dof6Coordinator(
            targetPos := targetPosition,
            sensorData := sensorFusion.fusedData,
            execute := TRUE
        );
        
        IF dof6Coordinator.done THEN
            IF dof6Coordinator.success THEN
                systemState := E_SystemState.VERIFICATION;
            ELSE
                systemState := E_SystemState.FINE_POSITIONING; // 重试
            END_IF
        END_IF
    
    E_SystemState.VERIFICATION:
        // 精度验证
        IF VerifyAccuracy(targetPosition, currentPosition, 5E-6) THEN
            positioningRequest.done := TRUE;
            positioningRequest.success := TRUE;
            systemState := E_SystemState.IDLE;
        ELSE
            systemState := E_SystemState.FINE_POSITIONING; // 重试
        END_IF
    
    E_SystemState.ERROR:
        // 错误处理
        HandleError();
        IF errorCleared THEN
            systemState := E_SystemState.IDLE;
        END_IF
END_CASE

// 安全监控（每个周期执行）
safetyMonitor();

// 传感器数据融合
sensorFusion();

// 与上位机通信
upperLevelComm();

// 性能监控
cycleTime := GETCYCLETIME();
IF cycleTime > maxCycleTime THEN
    maxCycleTime := cycleTime;
END_IF
```

### 1.3 应用逻辑层（SpringBoot）模块设计

#### 1.3.1 核心服务架构
```java
// 精密定位控制服务
@Service
@Slf4j
public class PrecisionPositioningService {
    
    @Autowired
    private TwinCATCommunicationService twinCATService;
    
    @Autowired
    private SensorDataService sensorDataService;
    
    @Autowired
    private AccuracyMonitorService accuracyMonitorService;
    
    // 执行精密定位
    @Async("positioningExecutor")
    public CompletableFuture<PositioningResult> executePositioning(
            PositioningRequest request) {
        
        try {
            log.info("Starting precision positioning: {}", request);
            
            // 1. 参数验证
            validatePositioningRequest(request);
            
            // 2. 系统状态检查
            SystemStatus status = twinCATService.getSystemStatus();
            if (!status.isReady()) {
                throw new SystemNotReadyException("System not ready for positioning");
            }
            
            // 3. 发送定位指令到TwinCAT
            PositioningCommand command = PositioningCommand.builder()
                .targetPosition(request.getTargetPosition())
                .accuracyRequirement(request.getAccuracyRequirement())
                .timeout(request.getTimeout())
                .build();
            
            twinCATService.sendPositioningCommand(command);
            
            // 4. 监控定位过程
            PositioningResult result = monitorPositioningProcess(command);
            
            // 5. 记录结果
            recordPositioningResult(request, result);
            
            return CompletableFuture.completedFuture(result);
            
        } catch (Exception e) {
            log.error("Positioning failed", e);
            return CompletableFuture.completedFuture(
                PositioningResult.failure(e.getMessage()));
        }
    }
    
    // 监控定位过程
    private PositioningResult monitorPositioningProcess(PositioningCommand command) {
        long startTime = System.currentTimeMillis();
        long timeout = command.getTimeout();
        
        while (System.currentTimeMillis() - startTime < timeout) {
            try {
                // 获取当前状态
                PositioningStatus status = twinCATService.getPositioningStatus();
                
                if (status.isCompleted()) {
                    if (status.isSuccess()) {
                        return PositioningResult.success(
                            status.getAchievedPosition(),
                            status.getAchievedAccuracy(),
                            System.currentTimeMillis() - startTime
                        );
                    } else {
                        return PositioningResult.failure(status.getErrorMessage());
                    }
                }
                
                // 实时数据记录
                recordRealtimeData(status);
                
                // 等待下一次检查
                Thread.sleep(100); // 100ms检查间隔
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return PositioningResult.failure("Positioning interrupted");
            }
        }
        
        return PositioningResult.failure("Positioning timeout");
    }
    
    // 获取当前精度状态
    public AccuracyStatus getCurrentAccuracyStatus() {
        try {
            // 从TwinCAT获取实时位置数据
            Position currentPosition = twinCATService.getCurrentPosition();
            
            // 从传感器服务获取精度数据
            AccuracyData accuracyData = sensorDataService.getCurrentAccuracy();
            
            return AccuracyStatus.builder()
                .currentPosition(currentPosition)
                .achievedAccuracy(accuracyData.getAccuracy())
                .sensorStatus(accuracyData.getSensorStatus())
                .timestamp(Instant.now())
                .build();
                
        } catch (Exception e) {
            log.error("Failed to get accuracy status", e);
            return AccuracyStatus.error(e.getMessage());
        }
    }
}
```

#### 1.3.2 TwinCAT通信服务
```java
// TwinCAT通信服务
@Service
@Slf4j
public class TwinCATCommunicationService {
    
    private AdsConnection adsConnection;
    private final Object connectionLock = new Object();
    
    @Value("${twincat.ams-net-id}")
    private String amsNetId;
    
    @Value("${twincat.port}")
    private int port;
    
    @PostConstruct
    public void initialize() {
        try {
            // 初始化ADS连接
            adsConnection = new AdsConnection(amsNetId, port);
            adsConnection.connect();
            
            log.info("TwinCAT connection established: {}:{}", amsNetId, port);
            
            // 启动心跳监控
            startHeartbeatMonitoring();
            
        } catch (Exception e) {
            log.error("Failed to initialize TwinCAT connection", e);
            throw new TwinCATConnectionException("Connection initialization failed", e);
        }
    }
    
    // 发送定位指令
    public void sendPositioningCommand(PositioningCommand command) {
        synchronized (connectionLock) {
            try {
                // 构建ADS写入数据
                ByteBuffer buffer = ByteBuffer.allocate(64);
                buffer.order(ByteOrder.LITTLE_ENDIAN);
                
                // 目标位置 (X, Y, Z)
                buffer.putDouble(command.getTargetPosition().getX());
                buffer.putDouble(command.getTargetPosition().getY());
                buffer.putDouble(command.getTargetPosition().getZ());
                
                // 精度要求
                buffer.putDouble(command.getAccuracyRequirement());
                
                // 超时时间
                buffer.putInt(command.getTimeout());
                
                // 执行标志
                buffer.put((byte) 1);
                
                // 写入到PLC
                adsConnection.write(
                    IndexGroup.PLCADS_IGR_RWMX,
                    getSymbolHandle("GVL_Communication.positioningRequest"),
                    buffer.array()
                );
                
                log.debug("Positioning command sent: {}", command);
                
            } catch (Exception e) {
                log.error("Failed to send positioning command", e);
                throw new TwinCATCommunicationException("Command send failed", e);
            }
        }
    }
    
    // 获取定位状态
    public PositioningStatus getPositioningStatus() {
        synchronized (connectionLock) {
            try {
                // 读取状态数据
                byte[] statusData = adsConnection.read(
                    IndexGroup.PLCADS_IGR_RWMX,
                    getSymbolHandle("GVL_Communication.positioningStatus"),
                    48 // 状态数据大小
                );
                
                ByteBuffer buffer = ByteBuffer.wrap(statusData);
                buffer.order(ByteOrder.LITTLE_ENDIAN);
                
                // 解析状态数据
                boolean isCompleted = buffer.get() != 0;
                boolean isSuccess = buffer.get() != 0;
                
                Position achievedPosition = Position.builder()
                    .x(buffer.getDouble())
                    .y(buffer.getDouble())
                    .z(buffer.getDouble())
                    .build();
                
                double achievedAccuracy = buffer.getDouble();
                int errorCode = buffer.getInt();
                
                return PositioningStatus.builder()
                    .completed(isCompleted)
                    .success(isSuccess)
                    .achievedPosition(achievedPosition)
                    .achievedAccuracy(achievedAccuracy)
                    .errorCode(errorCode)
                    .build();
                
            } catch (Exception e) {
                log.error("Failed to get positioning status", e);
                throw new TwinCATCommunicationException("Status read failed", e);
            }
        }
    }
    
    // 获取符号句柄
    private long getSymbolHandle(String symbolName) {
        try {
            return adsConnection.getSymbolHandle(symbolName);
        } catch (Exception e) {
            throw new TwinCATCommunicationException(
                "Failed to get symbol handle: " + symbolName, e);
        }
    }
    
    // 心跳监控
    @Scheduled(fixedRate = 1000) // 1秒间隔
    public void heartbeatMonitoring() {
        try {
            if (!adsConnection.isConnected()) {
                log.warn("TwinCAT connection lost, attempting reconnection...");
                reconnect();
            }
        } catch (Exception e) {
            log.error("Heartbeat monitoring failed", e);
        }
    }
    
    // 重连机制
    private void reconnect() {
        synchronized (connectionLock) {
            try {
                if (adsConnection != null) {
                    adsConnection.disconnect();
                }
                
                adsConnection = new AdsConnection(amsNetId, port);
                adsConnection.connect();
                
                log.info("TwinCAT reconnection successful");
                
            } catch (Exception e) {
                log.error("TwinCAT reconnection failed", e);
            }
        }
    }
}
```

### 1.4 人机交互层（Qt客户端）界面架构

#### 1.4.1 Qt应用程序结构
```cpp
// MainWindow.h - 主窗口类
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QtWidgets/QMainWindow>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QTabWidget>
#include <QtWebSockets/QWebSocket>
#include <QtNetwork/QNetworkAccessManager>
#include <QTimer>

#include "PrecisionControlWidget.h"
#include "ProcessMonitorWidget.h"
#include "DeviceStatusWidget.h"
#include "SystemConfigWidget.h"
#include "ApiClient.h"

QT_BEGIN_NAMESPACE
class QAction;
class QMenu;
class QStatusBar;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onPrecisionPositioningRequested(const PositionRequest& request);
    void onEmergencyStopTriggered();
    void onSystemStatusChanged(const SystemStatus& status);
    void onWebSocketConnected();
    void onWebSocketDisconnected();
    void onWebSocketMessageReceived(const QString& message);
    void updateSystemStatus();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupConnections();
    void connectToServer();

    // UI组件
    QTabWidget* m_tabWidget;
    PrecisionControlWidget* m_precisionWidget;
    ProcessMonitorWidget* m_processWidget;
    DeviceStatusWidget* m_deviceWidget;
    SystemConfigWidget* m_configWidget;
    
    // 网络通信
    QWebSocket* m_webSocket;
    ApiClient* m_apiClient;
    
    // 定时器
    QTimer* m_statusUpdateTimer;
    
    // 状态栏
    QLabel* m_connectionStatusLabel;
    QLabel* m_systemStatusLabel;
    QLabel* m_accuracyStatusLabel;
};

#endif // MAINWINDOW_H
```

#### 1.4.2 精密控制界面实现
```cpp
// PrecisionControlWidget.cpp - 精密控制界面
#include "PrecisionControlWidget.h"
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QLabel>
#include <QtCore/QTimer>

PrecisionControlWidget::PrecisionControlWidget(QWidget *parent)
    : QWidget(parent)
    , m_currentAccuracy(0.0)
    , m_isPositioning(false)
{
    setupUI();
    setupConnections();
    
    // 启动实时更新定时器
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &PrecisionControlWidget::updateDisplay);
    m_updateTimer->start(100); // 100ms更新间隔
}

void PrecisionControlWidget::setupUI()
{
    auto* mainLayout = new QVBoxLayout(this);
    
    // 目标位置设置组
    auto* targetGroup = new QGroupBox("目标位置设置", this);
    auto* targetLayout = new QGridLayout(targetGroup);
    
    // X轴位置
    targetLayout->addWidget(new QLabel("X轴位置 (mm):"), 0, 0);
    m_xPositionSpinBox = new QDoubleSpinBox();
    m_xPositionSpinBox->setRange(-100.0, 100.0);
    m_xPositionSpinBox->setDecimals(6);
    m_xPositionSpinBox->setSingleStep(0.001);
    targetLayout->addWidget(m_xPositionSpinBox, 0, 1);
    
    // Y轴位置
    targetLayout->addWidget(new QLabel("Y轴位置 (mm):"), 1, 0);
    m_yPositionSpinBox = new QDoubleSpinBox();
    m_yPositionSpinBox->setRange(-100.0, 100.0);
    m_yPositionSpinBox->setDecimals(6);
    m_yPositionSpinBox->setSingleStep(0.001);
    targetLayout->addWidget(m_yPositionSpinBox, 1, 1);
    
    // Z轴位置
    targetLayout->addWidget(new QLabel("Z轴位置 (mm):"), 2, 0);
    m_zPositionSpinBox = new QDoubleSpinBox();
    m_zPositionSpinBox->setRange(-50.0, 50.0);
    m_zPositionSpinBox->setDecimals(6);
    m_zPositionSpinBox->setSingleStep(0.001);
    targetLayout->addWidget(m_zPositionSpinBox, 2, 1);
    
    // 精度要求
    targetLayout->addWidget(new QLabel("精度要求 (μm):"), 3, 0);
    m_accuracySpinBox = new QDoubleSpinBox();
    m_accuracySpinBox->setRange(1.0, 50.0);
    m_accuracySpinBox->setDecimals(1);
    m_accuracySpinBox->setValue(5.0);
    targetLayout->addWidget(m_accuracySpinBox, 3, 1);
    
    mainLayout->addWidget(targetGroup);
    
    // 当前状态显示组
    auto* statusGroup = new QGroupBox("当前状态", this);
    auto* statusLayout = new QGridLayout(statusGroup);
    
    // 当前位置显示
    statusLayout->addWidget(new QLabel("当前X位置:"), 0, 0);
    m_currentXLabel = new QLabel("0.000000 mm");
    m_currentXLabel->setStyleSheet("font-family: monospace; font-weight: bold;");
    statusLayout->addWidget(m_currentXLabel, 0, 1);
    
    statusLayout->addWidget(new QLabel("当前Y位置:"), 1, 0);
    m_currentYLabel = new QLabel("0.000000 mm");
    m_currentYLabel->setStyleSheet("font-family: monospace; font-weight: bold;");
    statusLayout->addWidget(m_currentYLabel, 1, 1);
    
    statusLayout->addWidget(new QLabel("当前Z位置:"), 2, 0);
    m_currentZLabel = new QLabel("0.000000 mm");
    m_currentZLabel->setStyleSheet("font-family: monospace; font-weight: bold;");
    statusLayout->addWidget(m_currentZLabel, 2, 1);
    
    // 精度显示
    statusLayout->addWidget(new QLabel("当前精度:"), 3, 0);
    m_accuracyLabel = new QLabel("-- μm");
    m_accuracyLabel->setStyleSheet("font-family: monospace; font-weight: bold; font-size: 14px;");
    statusLayout->addWidget(m_accuracyLabel, 3, 1);
    
    // 精度指示器
    m_accuracyProgressBar = new QProgressBar();
    m_accuracyProgressBar->setRange(0, 100);
    m_accuracyProgressBar->setFormat("精度指示: %p%");
    statusLayout->addWidget(m_accuracyProgressBar, 4, 0, 1, 2);
    
    mainLayout->addWidget(statusGroup);
    
    // 控制按钮组
    auto* controlGroup = new QGroupBox("控制操作", this);
    auto* controlLayout = new QHBoxLayout(controlGroup);
    
    m_startButton = new QPushButton("开始定位");
    m_startButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }");
    controlLayout->addWidget(m_startButton);
    
    m_stopButton = new QPushButton("停止");
    m_stopButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 10px; }");
    m_stopButton->setEnabled(false);
    controlLayout->addWidget(m_stopButton);
    
    m_homeButton = new QPushButton("回零点");
    m_homeButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 10px; }");
    controlLayout->addWidget(m_homeButton);
    
    mainLayout->addWidget(controlGroup);
}

void PrecisionControlWidget::setupConnections()
{
    connect(m_startButton, &QPushButton::clicked, this, &PrecisionControlWidget::onStartPositioning);
    connect(m_stopButton, &QPushButton::clicked, this, &PrecisionControlWidget::onStopPositioning);
    connect(m_homeButton, &QPushButton::clicked, this, &PrecisionControlWidget::onHomePosition);
}

void PrecisionControlWidget::onStartPositioning()
{
    if (m_isPositioning) {
        return;
    }
    
    // 构建定位请求
    PositionRequest request;
    request.targetPosition.x = m_xPositionSpinBox->value();
    request.targetPosition.y = m_yPositionSpinBox->value();
    request.targetPosition.z = m_zPositionSpinBox->value();
    request.accuracyRequirement = m_accuracySpinBox->value() * 1e-6; // 转换为米
    request.timeout = 30000; // 30秒超时
    
    // 发送定位请求信号
    emit positioningRequested(request);
    
    // 更新UI状态
    m_isPositioning = true;
    m_startButton->setEnabled(false);
    m_stopButton->setEnabled(true);
    
    // 开始进度动画
    startProgressAnimation();
}

void PrecisionControlWidget::updateDisplay()
{
    // 更新当前位置显示
    m_currentXLabel->setText(QString("%1 mm").arg(m_currentPosition.x, 0, 'f', 6));
    m_currentYLabel->setText(QString("%1 mm").arg(m_currentPosition.y, 0, 'f', 6));
    m_currentZLabel->setText(QString("%1 mm").arg(m_currentPosition.z, 0, 'f', 6));
    
    // 更新精度显示
    if (m_currentAccuracy > 0) {
        m_accuracyLabel->setText(QString("±%1 μm").arg(m_currentAccuracy * 1e6, 0, 'f', 2));
        
        // 精度指示器颜色
        double accuracyPercent = std::min(100.0, (5.0 / (m_currentAccuracy * 1e6)) * 100.0);
        m_accuracyProgressBar->setValue(static_cast<int>(accuracyPercent));
        
        if (m_currentAccuracy <= 5e-6) {
            m_accuracyLabel->setStyleSheet("color: green; font-family: monospace; font-weight: bold; font-size: 14px;");
            m_accuracyProgressBar->setStyleSheet("QProgressBar::chunk { background-color: #4CAF50; }");
        } else if (m_currentAccuracy <= 10e-6) {
            m_accuracyLabel->setStyleSheet("color: orange; font-family: monospace; font-weight: bold; font-size: 14px;");
            m_accuracyProgressBar->setStyleSheet("QProgressBar::chunk { background-color: #FF9800; }");
        } else {
            m_accuracyLabel->setStyleSheet("color: red; font-family: monospace; font-weight: bold; font-size: 14px;");
            m_accuracyProgressBar->setStyleSheet("QProgressBar::chunk { background-color: #f44336; }");
        }
    }
}

void PrecisionControlWidget::onPositioningCompleted(const PositioningResult& result)
{
    m_isPositioning = false;
    m_startButton->setEnabled(true);
    m_stopButton->setEnabled(false);
    
    stopProgressAnimation();
    
    if (result.success) {
        // 显示成功消息
        QMessageBox::information(this, "定位完成", 
            QString("定位成功完成！\n"
                   "达到精度: ±%1 μm\n"
                   "用时: %2 ms")
            .arg(result.achievedAccuracy * 1e6, 0, 'f', 2)
            .arg(result.duration));
    } else {
        // 显示错误消息
        QMessageBox::warning(this, "定位失败", 
            QString("定位失败: %1").arg(result.errorMessage));
    }
}
```

### 1.5 各层通信接口和数据流

#### 1.5.1 通信协议定义
```yaml
# 通信接口配置
communication:
  # TwinCAT通信
  twincat:
    ams-net-id: "*************.1.1"
    port: 851
    timeout: 5000
    heartbeat-interval: 1000
  
  # WebSocket通信
  websocket:
    port: 8081
    path: "/precision-control"
    heartbeat-interval: 30000
  
  # REST API
  rest-api:
    base-url: "http://localhost:8080/api"
    timeout: 10000
  
  # MQTT通信
  mqtt:
    broker: "tcp://localhost:1883"
    client-id: "precision-control-system"
    topics:
      positioning-data: "precision/positioning/data"
      system-status: "precision/system/status"
      alerts: "precision/alerts"
```

#### 1.5.2 数据流设计
```
实时数据流：
┌─────────────────┐    100Hz    ┌─────────────────┐    10Hz     ┌─────────────────┐
│   传感器硬件     │ ────────▶  │   TwinCAT PLC   │ ────────▶  │  SpringBoot     │
│  (光栅尺/电容)   │            │   (实时控制)     │            │   (业务逻辑)     │
└─────────────────┘            └─────────────────┘            └─────────────────┘
                                        │                              │
                                        │ 1Hz                         │ WebSocket
                                        ▼                              ▼
                               ┌─────────────────┐            ┌─────────────────┐
                               │   数据记录       │            │   Qt客户端      │
                               │  (InfluxDB)     │            │  (人机界面)     │
                               └─────────────────┘            └─────────────────┘

控制指令流：
┌─────────────────┐  WebSocket  ┌─────────────────┐  EtherCAT  ┌─────────────────┐
│   Qt客户端      │ ────────▶  │  SpringBoot     │ ────────▶  │   TwinCAT PLC   │
│  (操作界面)     │            │   (指令处理)     │            │   (运动控制)     │
└─────────────────┘            └─────────────────┘            └─────────────────┘
                                        │                              │
                                        │ HTTP                        │ EtherCAT I/O
                                        ▼                              ▼
                               ┌─────────────────┐            ┌─────────────────┐
                               │   MES系统       │            │   伺服驱动器     │
                               │  (工单管理)     │            │  (电机控制)     │
                               └─────────────────┘            └─────────────────┘
```

---

## 🧮 核心控制算法实现

### 2.1 分层控制策略具体代码实现

#### 2.1.1 粗定位控制算法
```pascal
// CoarsePositioning.ST - 粗定位控制
FUNCTION_BLOCK CoarsePositioning
VAR_INPUT
    targetPos : ST_Position;        // 目标位置
    execute : BOOL;                 // 执行标志
    reset : BOOL;                   // 复位标志
END_VAR

VAR_OUTPUT
    done : BOOL;                    // 完成标志
    success : BOOL;                 // 成功标志
    error : BOOL;                   // 错误标志
    errorID : DWORD;               // 错误代码
END_VAR

VAR
    // 当前状态
    state : E_CoarsePositioningState := E_CoarsePositioningState.IDLE;
    
    // 运动控制功能块
    mcMoveAbs_X : MC_MoveAbsolute;
    mcMoveAbs_Y : MC_MoveAbsolute;
    mcMoveAbs_Z : MC_MoveAbsolute;
    
    // 控制参数
    maxVelocity : LREAL := 0.05;    // 50mm/s
    acceleration : LREAL := 0.5;     // 0.5m/s²
    coarseTolerance : LREAL := 5E-6; // ±5μm
    
    // 当前位置
    currentPos : ST_Position;
    
    // 定时器
    timeoutTimer : TON;
    stabilizeTimer : TON;
    
    // 内部变量
    allAxesInPosition : BOOL;
    positionError : ARRAY[1..3] OF LREAL;
    
    // 性能监控
    startTime : TIME;
    positioningTime : TIME;
END_VAR

// 主控制逻辑
CASE state OF
    E_CoarsePositioningState.IDLE:
        // 初始状态
        done := FALSE;
        success := FALSE;
        error := FALSE;
        
        IF execute AND NOT reset THEN
            state := E_CoarsePositioningState.PREPARE;
            startTime := TIME();
        END_IF
    
    E_CoarsePositioningState.PREPARE:
        // 准备阶段
        // 检查轴状态
        IF CheckAxesReady() THEN
            state := E_CoarsePositioningState.MOVING;
            timeoutTimer(IN := TRUE, PT := T#30S);
        ELSE
            state := E_CoarsePositioningState.ERROR;
            errorID := 16#1001; // 轴未就绪
        END_IF
    
    E_CoarsePositioningState.MOVING:
        // 运动阶段
        // X轴运动
        mcMoveAbs_X(
            Axis := GVL_Motion.axisX,
            Execute := TRUE,
            Position := targetPos.x,
            Velocity := maxVelocity,
            Acceleration := acceleration,
            Deceleration := acceleration
        );
        
        // Y轴运动
        mcMoveAbs_Y(
            Axis := GVL_Motion.axisY,
            Execute := TRUE,
            Position := targetPos.y,
            Velocity := maxVelocity,
            Acceleration := acceleration,
            Deceleration := acceleration
        );
        
        // Z轴运动
        mcMoveAbs_Z(
            Axis := GVL_Motion.axisZ,
            Execute := TRUE,
            Position := targetPos.z,
            Velocity := maxVelocity,
            Acceleration := acceleration,
            Deceleration := acceleration
        );
        
        // 检查运动完成
        IF mcMoveAbs_X.Done AND mcMoveAbs_Y.Done AND mcMoveAbs_Z.Done THEN
            state := E_CoarsePositioningState.CHECKING;
            stabilizeTimer(IN := TRUE, PT := T#500MS); // 稳定时间
        END_IF
        
        // 检查错误
        IF mcMoveAbs_X.Error OR mcMoveAbs_Y.Error OR mcMoveAbs_Z.Error THEN
            state := E_CoarsePositioningState.ERROR;
            errorID := 16#1002; // 运动错误
        END_IF
        
        // 超时检查
        timeoutTimer();
        IF timeoutTimer.Q THEN
            state := E_CoarsePositioningState.ERROR;
            errorID := 16#1003; // 超时错误
        END_IF
    
    E_CoarsePositioningState.CHECKING:
        // 精度检查阶段
        stabilizeTimer();
        
        IF stabilizeTimer.Q THEN
            // 读取当前位置
            currentPos.x := GVL_Sensors.opticalEncoder_X.position;
            currentPos.y := GVL_Sensors.opticalEncoder_Y.position;
            currentPos.z := GVL_Sensors.opticalEncoder_Z.position;
            
            // 计算位置误差
            positionError[1] := ABS(targetPos.x - currentPos.x);
            positionError[2] := ABS(targetPos.y - currentPos.y);
            positionError[3] := ABS(targetPos.z - currentPos.z);
            
            // 检查精度
            allAxesInPosition := 
                (positionError[1] <= coarseTolerance) AND
                (positionError[2] <= coarseTolerance) AND
                (positionError[3] <= coarseTolerance);
            
            IF allAxesInPosition THEN
                state := E_CoarsePositioningState.COMPLETE;
            ELSE
                state := E_CoarsePositioningState.ERROR;
                errorID := 16#1004; // 精度不达标
            END_IF
        END_IF
    
    E_CoarsePositioningState.COMPLETE:
        // 完成状态
        done := TRUE;
        success := TRUE;
        positioningTime := TIME() - startTime;
        
        IF NOT execute THEN
            state := E_CoarsePositioningState.IDLE;
        END_IF
    
    E_CoarsePositioningState.ERROR:
        // 错误状态
        done := TRUE;
        error := TRUE;
        
        // 停止所有运动
        mcMoveAbs_X(Execute := FALSE);
        mcMoveAbs_Y(Execute := FALSE);
        mcMoveAbs_Z(Execute := FALSE);
        
        IF reset THEN
            state := E_CoarsePositioningState.IDLE;
        END_IF
END_CASE

// 复位处理
IF reset THEN
    state := E_CoarsePositioningState.IDLE;
    timeoutTimer(IN := FALSE);
    stabilizeTimer(IN := FALSE);
END_IF
```

#### 2.1.2 精定位控制算法
```cpp
// 精定位控制算法实现
class FinePositioningController {
private:
    // PID控制器数组（6DOF）
    std::array<PIDController, 6> pidControllers;
    
    // 电容传感器接口
    std::array<CapacitiveSensor, 6> capacitiveSensors;
    
    // 微调执行器
    std::array<PiezoActuator, 6> piezoActuators;
    
    // 控制参数
    struct ControlParams {
        double fineTolerance = 1e-6;     // ±1μm精度
        double maxCorrection = 10e-6;    // 最大微调量±10μm
        int maxIterations = 50;          // 最大迭代次数
        double convergenceRate = 0.1;    // 收敛速率
    } params;
    
    // 状态变量
    bool isActive = false;
    int currentIteration = 0;
    std::chrono::steady_clock::time_point startTime;
    
public:
    // 执行精定位
    PositioningResult executeFinePositioning(const TargetPosition& target) {
        isActive = true;
        currentIteration = 0;
        startTime = std::chrono::steady_clock::now();
        
        try {
            // 初始化PID控制器
            initializePIDControllers();
            
            // 迭代控制循环
            while (currentIteration < params.maxIterations && isActive) {
                // 读取传感器数据
                SensorData sensorData = readSensorData();
                
                // 计算位置误差
                ErrorVector errors = calculatePositionErrors(target, sensorData);
                
                // 检查收敛条件
                if (isConverged(errors)) {
                    return createSuccessResult(target, sensorData);
                }
                
                // PID控制计算
                CorrectionVector corrections = calculateCorrections(errors);
                
                // 应用微调
                applyCorrections(corrections);
                
                // 等待稳定
                std::this_thread::sleep_for(std::chrono::milliseconds(20));
                
                currentIteration++;
            }
            
            // 超过最大迭代次数
            return PositioningResult::failure("Fine positioning failed: max iterations exceeded");
            
        } catch (const std::exception& e) {
            return PositioningResult::failure("Fine positioning error: " + std::string(e.what()));
        }
    }
    
private:
    // 初始化PID控制器
    void initializePIDControllers() {
        // X, Y, Z轴位置控制参数
        PIDParams positionParams{500.0, 20.0, 5.0, -100.0, 100.0, 20.0};
        
        for (int i = 0; i < 3; i++) {
            pidControllers[i].setParameters(positionParams);
            pidControllers[i].reset();
        }
        
        // Rx, Ry, Rz轴角度控制参数
        PIDParams angleParams{300.0, 15.0, 3.0, -50.0, 50.0, 15.0};
        
        for (int i = 3; i < 6; i++) {
            pidControllers[i].setParameters(angleParams);
            pidControllers[i].reset();
        }
    }
    
    // 读取传感器数据
    SensorData readSensorData() {
        SensorData data;
        
        for (int i = 0; i < 6; i++) {
            try {
                // 读取电容传感器数据
                double rawValue = capacitiveSensors[i].readPosition();
                
                // 温度补偿
                double temperature = capacitiveSensors[i].readTemperature();
                double compensatedValue = temperatureCompensation(rawValue, temperature);
                
                // 滤波处理
                data.positions[i] = lowPassFilter(compensatedValue, i);
                data.valid[i] = true;
                
            } catch (const std::exception& e) {
                data.valid[i] = false;
                logger.warn("Sensor {} read failed: {}", i, e.what());
            }
        }
        
        data.timestamp = std::chrono::steady_clock::now();
        return data;
    }
    
    // 计算位置误差
    ErrorVector calculatePositionErrors(const TargetPosition& target, const SensorData& current) {
        ErrorVector errors;
        
        // 位置误差 (X, Y, Z)
        errors.position[0] = target.x - current.positions[0];
        errors.position[1] = target.y - current.positions[1];
        errors.position[2] = target.z - current.positions[2];
        
        // 角度误差 (Rx, Ry, Rz)
        errors.angle[0] = target.rx - current.positions[3];
        errors.angle[1] = target.ry - current.positions[4];
        errors.angle[2] = target.rz - current.positions[5];
        
        // 计算总误差
        errors.magnitude = std::sqrt(
            errors.position[0] * errors.position[0] +
            errors.position[1] * errors.position[1] +
            errors.position[2] * errors.position[2]
        );
        
        return errors;
    }
    
    // PID控制计算
    CorrectionVector calculateCorrections(const ErrorVector& errors) {
        CorrectionVector corrections;
        
        for (int i = 0; i < 6; i++) {
            double error = (i < 3) ? errors.position[i] : errors.angle[i - 3];
            
            // PID计算
            double correction = pidControllers[i].calculate(error);
            
            // 限制微调范围
            correction = std::clamp(correction, -params.maxCorrection, params.maxCorrection);
            
            corrections.values[i] = correction;
        }
        
        return corrections;
    }
    
    // 应用微调
    void applyCorrections(const CorrectionVector& corrections) {
        for (int i = 0; i < 6; i++) {
            try {
                piezoActuators[i].move(corrections.values[i]);
            } catch (const std::exception& e) {
                logger.error("Actuator {} move failed: {}", i, e.what());
            }
        }
    }
    
    // 检查收敛条件
    bool isConverged(const ErrorVector& errors) {
        return errors.magnitude <= params.fineTolerance;
    }
    
    // 温度补偿
    double temperatureCompensation(double rawValue, double temperature) {
        const double TEMP_COEFF = 50e-6; // 50ppm/K
        const double REF_TEMP = 20.0;    // 参考温度20°C
        
        double tempDiff = temperature - REF_TEMP;
        return rawValue * (1.0 - TEMP_COEFF * tempDiff);
    }
    
    // 低通滤波器
    double lowPassFilter(double newValue, int channelIndex) {
        static std::array<double, 6> previousValues = {0};
        const double FILTER_COEFF = 0.8;
        
        double filteredValue = FILTER_COEFF * previousValues[channelIndex] + 
                              (1.0 - FILTER_COEFF) * newValue;
        
        previousValues[channelIndex] = filteredValue;
        return filteredValue;
    }
};
```

### 2.2 PID控制算法参数配置和自适应调整

#### 2.2.1 自适应PID控制器实现
```cpp
// 自适应PID控制器
class AdaptivePIDController {
private:
    // PID参数
    struct PIDParams {
        double kp, ki, kd;
        double integralMin, integralMax;
        double derivativeFilterFreq;
    } params;
    
    // 自适应参数
    struct AdaptiveParams {
        double learningRate = 0.01;
        double adaptationThreshold = 1e-6;
        double parameterBounds[6] = {10.0, 5000.0, 0.1, 100.0, 0.01, 50.0}; // kp_min, kp_max, ki_min, ki_max, kd_min, kd_max
    } adaptiveParams;
    
    // 状态变量
    double previousError = 0.0;
    double integral = 0.0;
    double derivative = 0.0;
    std::chrono::steady_clock::time_point lastTime;
    
    // 误差历史（用于自适应）
    std::deque<double> errorHistory;
    static constexpr size_t HISTORY_SIZE = 20;
    
    // 性能指标
    double performanceIndex = 0.0;
    double previousPerformanceIndex = 0.0;
    
public:
    AdaptivePIDController(double kp, double ki, double kd) {
        params.kp = kp;
        params.ki = ki;
        params.kd = kd;
        params.integralMin = -1000.0;
        params.integralMax = 1000.0;
        params.derivativeFilterFreq = 10.0;
        
        lastTime = std::chrono::steady_clock::now();
    }
    
    // 主控制计算函数
    double calculate(double error) {
        auto currentTime = std::chrono::steady_clock::now();
        auto deltaTime = std::chrono::duration<double>(currentTime - lastTime).count();
        
        if (deltaTime <= 0.0) {
            return 0.0;
        }
        
        // 比例项
        double proportional = params.kp * error;
        
        // 积分项
        integral += error * deltaTime;
        integral = std::clamp(integral, params.integralMin, params.integralMax);
        double integralTerm = params.ki * integral;
        
        // 微分项（带滤波）
        double rawDerivative = (error - previousError) / deltaTime;
        derivative = lowPassFilter(rawDerivative, params.derivativeFilterFreq, deltaTime);
        double derivativeTerm = params.kd * derivative;
        
        // PID输出
        double output = proportional + integralTerm + derivativeTerm;
        
        // 更新历史数据
        updateErrorHistory(error);
        
        // 自适应参数调整
        if (errorHistory.size() >= HISTORY_SIZE) {
            adaptParameters(error);
        }
        
        // 更新状态
        previousError = error;
        lastTime = currentTime;
        
        return output;
    }
    
    // 重置控制器
    void reset() {
        previousError = 0.0;
        integral = 0.0;
        derivative = 0.0;
        errorHistory.clear();
        lastTime = std::chrono::steady_clock::now();
    }
    
    // 设置参数
    void setParameters(double kp, double ki, double kd) {
        params.kp = kp;
        params.ki = ki;
        params.kd = kd;
    }
    
    // 获取当前参数
    PIDParams getParameters() const {
        return params;
    }
    
private:
    // 更新误差历史
    void updateErrorHistory(double error) {
        errorHistory.push_back(error);
        if (errorHistory.size() > HISTORY_SIZE) {
            errorHistory.pop_front();
        }
    }
    
    // 自适应参数调整
    void adaptParameters(double currentError) {
        // 计算性能指标（基于误差的积分绝对值）
        double currentPerformanceIndex = calculatePerformanceIndex();
        
        // 如果性能指标改善，继续当前调整方向
        // 如果性能指标恶化，反向调整
        
        double performanceChange = currentPerformanceIndex - previousPerformanceIndex;
        
        if (std::abs(currentError) > adaptiveParams.adaptationThreshold) {
            // 需要调整参数
            
            if (std::abs(currentError) > 5e-6) {
                // 误差较大，增加比例增益
                adjustParameter(params.kp, 0.05, adaptiveParams.parameterBounds[0], adaptiveParams.parameterBounds[1]);
            } else if (std::abs(currentError) < 1e-6) {
                // 误差较小，可能存在振荡，减少微分增益
                adjustParameter(params.kd, -0.02, adaptiveParams.parameterBounds[4], adaptiveParams.parameterBounds[5]);
            }
            
            // 根据误差趋势调整积分增益
            double errorTrend = calculateErrorTrend();
            if (errorTrend > 0 && std::abs(currentError) > 2e-6) {
                // 误差增大趋势，增加积分增益
                adjustParameter(params.ki, 0.02, adaptiveParams.parameterBounds[2], adaptiveParams.parameterBounds[3]);
            } else if (errorTrend < 0 && std::abs(currentError) < 1e-6) {
                // 误差减小且接近目标，减少积分增益避免超调
                adjustParameter(params.ki, -0.01, adaptiveParams.parameterBounds[2], adaptiveParams.parameterBounds[3]);
            }
        }
        
        previousPerformanceIndex = currentPerformanceIndex;
    }
    
    // 计算性能指标
    double calculatePerformanceIndex() {
        if (errorHistory.empty()) {
            return 0.0;
        }
        
        double sum = 0.0;
        for (double error : errorHistory) {
            sum += std::abs(error);
        }
        
        return sum / errorHistory.size();
    }
    
    // 计算误差趋势
    double calculateErrorTrend() {
        if (errorHistory.size() < 10) {
            return 0.0;
        }
        
        // 计算最近10个点的线性趋势
        double sumX = 0.0, sumY = 0.0, sumXY = 0.0, sumX2 = 0.0;
        int n = 10;
        
        for (int i = 0; i < n; i++) {
            double x = i;
            double y = errorHistory[errorHistory.size() - n + i];
            
            sumX += x;
            sumY += y;
            sumXY += x * y;
            sumX2 += x * x;
        }
        
        // 线性回归斜率
        double slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        return slope;
    }
    
    // 调整单个参数
    void adjustParameter(double& parameter, double adjustment, double minValue, double maxValue) {
        parameter += adjustment * adaptiveParams.learningRate;
        parameter = std::clamp(parameter, minValue, maxValue);
    }
    
    // 低通滤波器
    double lowPassFilter(double input, double cutoffFreq, double deltaTime) {
        double alpha = deltaTime / (deltaTime + 1.0 / (2.0 * M_PI * cutoffFreq));
        static double previousOutput = 0.0;
        
        double output = alpha * input + (1.0 - alpha) * previousOutput;
        previousOutput = output;
        
        return output;
    }
};
```

### 2.3 多传感器数据融合算法

#### 2.3.1 卡尔曼滤波数据融合实现
```cpp
// 多传感器数据融合系统
class MultiSensorFusionSystem {
private:
    // 扩展卡尔曼滤波器
    ExtendedKalmanFilter ekf;

    // 传感器接口
    std::array<ServoEncoder, 3> servoEncoders;        // 23bit伺服编码器
    std::array<CapacitiveSensor, 6> capacitiveSensors; // 电容传感器
    std::array<TemperatureSensor, 6> temperatureSensors; // 温度传感器（增加数量）

    // 融合参数
    struct FusionParams {
        double opticalWeight = 0.6;      // 光栅尺权重
        double capacitiveWeight = 0.4;   // 电容传感器权重
        double temperatureCoeff = 50e-6; // 温度系数
        double outlierThreshold = 3.0;   // 异常值阈值（3σ）
    } fusionParams;

    // 状态向量 [x, y, z, rx, ry, rz, vx, vy, vz, wx, wy, wz]
    Eigen::VectorXd state;
    Eigen::MatrixXd P; // 协方差矩阵

public:
    MultiSensorFusionSystem() {
        initializeKalmanFilter();
    }

    // 执行传感器数据融合
    FusedSensorData fuseSensorData() {
        try {
            // 1. 采集原始传感器数据
            RawSensorData rawData = collectRawSensorData();

            // 2. 数据预处理
            ProcessedSensorData processedData = preprocessSensorData(rawData);

            // 3. 异常值检测和处理
            ValidatedSensorData validatedData = validateSensorData(processedData);

            // 4. 卡尔曼滤波融合
            FusedSensorData fusedData = performKalmanFusion(validatedData);

            // 5. 后处理和校准
            fusedData = postProcessFusedData(fusedData);

            return fusedData;

        } catch (const std::exception& e) {
            logger.error("Sensor fusion failed: {}", e.what());
            return FusedSensorData::invalid();
        }
    }

private:
    // 初始化卡尔曼滤波器
    void initializeKalmanFilter() {
        // 状态向量初始化 [位置, 角度, 速度, 角速度]
        state = Eigen::VectorXd::Zero(12);

        // 初始协方差矩阵
        P = Eigen::MatrixXd::Identity(12, 12) * 0.1;

        // 过程噪声矩阵Q
        Eigen::MatrixXd Q = Eigen::MatrixXd::Identity(12, 12);
        Q.diagonal() << 1e-8, 1e-8, 1e-8,  // 位置过程噪声
                       1e-9, 1e-9, 1e-9,  // 角度过程噪声
                       1e-6, 1e-6, 1e-6,  // 速度过程噪声
                       1e-7, 1e-7, 1e-7;  // 角速度过程噪声

        ekf.setProcessNoise(Q);

        // 测量噪声矩阵R
        Eigen::MatrixXd R = Eigen::MatrixXd::Identity(9, 9);
        R.diagonal() << 3e-7, 3e-7, 3e-7,  // 23bit编码器测量噪声
                       3e-8, 3e-8, 3e-8,  // 电容传感器测量噪声
                       1e-6, 1e-6, 1e-6;  // 角度传感器测量噪声

        ekf.setMeasurementNoise(R);
    }

    // 采集原始传感器数据
    RawSensorData collectRawSensorData() {
        RawSensorData data;
        data.timestamp = std::chrono::steady_clock::now();

        // 读取23bit伺服编码器数据
        for (int i = 0; i < 3; i++) {
            data.servoPositions[i] = servoEncoders[i].readPosition();
            data.servoValid[i] = servoEncoders[i].isValid();
        }

        // 读取电容传感器数据
        for (int i = 0; i < 6; i++) {
            data.capacitivePositions[i] = capacitiveSensors[i].readPosition();
            data.capacitiveValid[i] = capacitiveSensors[i].isValid();
        }

        // 读取温度数据（增加到6个传感器）
        for (int i = 0; i < 6; i++) {
            data.temperatures[i] = temperatureSensors[i].readTemperature();
            data.temperatureValid[i] = temperatureSensors[i].isValid();
        }

        return data;
    }

    // 数据预处理
    ProcessedSensorData preprocessSensorData(const RawSensorData& rawData) {
        ProcessedSensorData processed;
        processed.timestamp = rawData.timestamp;

        // 计算平均温度
        double avgTemperature = calculateAverageTemperature(rawData);

        // 23bit伺服编码器数据温度补偿
        for (int i = 0; i < 3; i++) {
            if (rawData.servoValid[i]) {
                processed.servoPositions[i] = temperatureCompensation(
                    rawData.servoPositions[i], avgTemperature);
                processed.servoValid[i] = true;
            }
        }

        // 电容传感器数据处理
        for (int i = 0; i < 6; i++) {
            if (rawData.capacitiveValid[i]) {
                // 温度补偿
                double tempCompensated = temperatureCompensation(
                    rawData.capacitivePositions[i], avgTemperature);

                // 低通滤波
                processed.capacitivePositions[i] = lowPassFilter(tempCompensated, i);
                processed.capacitiveValid[i] = true;
            }
        }

        return processed;
    }

    // 异常值检测
    ValidatedSensorData validateSensorData(const ProcessedSensorData& processedData) {
        ValidatedSensorData validated = processedData;

        // 伺服编码器数据一致性检查
        validateServoEncoderConsistency(validated);

        // 电容传感器数据范围检查
        validateCapacitiveSensorRange(validated);

        // 传感器间交叉验证
        performCrossValidation(validated);

        return validated;
    }

    // 卡尔曼滤波融合
    FusedSensorData performKalmanFusion(const ValidatedSensorData& validatedData) {
        // 预测步骤
        double dt = calculateDeltaTime(validatedData.timestamp);
        ekf.predict(dt);

        // 构建测量向量
        Eigen::VectorXd measurement = buildMeasurementVector(validatedData);

        // 更新步骤
        ekf.update(measurement);

        // 获取融合结果
        Eigen::VectorXd fusedState = ekf.getState();
        Eigen::MatrixXd fusedCovariance = ekf.getCovariance();

        // 构建融合数据结构
        FusedSensorData fusedData;
        fusedData.timestamp = validatedData.timestamp;
        fusedData.position = {fusedState(0), fusedState(1), fusedState(2)};
        fusedData.orientation = {fusedState(3), fusedState(4), fusedState(5)};
        fusedData.velocity = {fusedState(6), fusedState(7), fusedState(8)};
        fusedData.angularVelocity = {fusedState(9), fusedState(10), fusedState(11)};

        // 计算不确定度
        fusedData.positionUncertainty = std::sqrt(
            fusedCovariance(0,0) + fusedCovariance(1,1) + fusedCovariance(2,2));

        fusedData.valid = true;

        return fusedData;
    }

    // 温度补偿
    double temperatureCompensation(double rawValue, double temperature) {
        const double REF_TEMP = 20.0; // 参考温度
        double tempDiff = temperature - REF_TEMP;
        return rawValue * (1.0 - fusionParams.temperatureCoeff * tempDiff);
    }

    // 低通滤波
    double lowPassFilter(double newValue, int channelIndex) {
        static std::array<double, 6> previousValues = {0};
        const double ALPHA = 0.8; // 滤波系数

        double filteredValue = ALPHA * previousValues[channelIndex] +
                              (1.0 - ALPHA) * newValue;

        previousValues[channelIndex] = filteredValue;
        return filteredValue;
    }
};
```

### 2.4 6DOF协调控制数学模型和实现

#### 2.4.1 6DOF运动学模型
```cpp
// 6DOF协调控制系统
class DOF6CoordinatedController {
private:
    // 运动学参数
    struct KinematicParams {
        double platformRadius = 0.1;     // 平台半径
        double sensorSpacing = 0.05;     // 传感器间距
        Eigen::Matrix3d rotationMatrix;  // 旋转矩阵
        Eigen::Vector3d translationVector; // 平移向量
    } kinematicParams;

    // 控制参数
    struct ControlParams {
        double positionGain = 1000.0;    // 位置增益
        double orientationGain = 500.0;  // 姿态增益
        double dampingRatio = 0.7;       // 阻尼比
        double naturalFrequency = 50.0;  // 自然频率
    } controlParams;

    // 雅可比矩阵
    Eigen::Matrix<double, 6, 6> jacobianMatrix;

public:
    // 执行6DOF协调控制
    ControlResult execute6DOFControl(const DOF6Target& target, const SensorArray& sensorData) {
        try {
            // 1. 计算当前位姿
            Pose6DOF currentPose = calculateCurrentPose(sensorData);

            // 2. 计算位姿误差
            PoseError poseError = calculatePoseError(target.pose, currentPose);

            // 3. 雅可比矩阵计算
            updateJacobianMatrix(currentPose);

            // 4. 控制律计算
            ControlForces controlForces = calculateControlForces(poseError);

            // 5. 执行器指令分配
            ActuatorCommands commands = distributeActuatorCommands(controlForces);

            // 6. 执行控制指令
            executeActuatorCommands(commands);

            return ControlResult::success(currentPose, poseError.magnitude());

        } catch (const std::exception& e) {
            return ControlResult::failure(e.what());
        }
    }

private:
    // 计算当前位姿
    Pose6DOF calculateCurrentPose(const SensorArray& sensorData) {
        // 从6个电容传感器数据计算6DOF位姿
        Eigen::VectorXd sensorValues(6);
        for (int i = 0; i < 6; i++) {
            sensorValues(i) = sensorData.values[i];
        }

        // 传感器数据到位姿的转换
        // 使用最小二乘法求解位姿
        Eigen::VectorXd pose = sensorToPositionTransform(sensorValues);

        Pose6DOF currentPose;
        currentPose.position = {pose(0), pose(1), pose(2)};
        currentPose.orientation = {pose(3), pose(4), pose(5)};
        currentPose.timestamp = std::chrono::steady_clock::now();

        return currentPose;
    }

    // 传感器数据到位姿转换
    Eigen::VectorXd sensorToPositionTransform(const Eigen::VectorXd& sensorValues) {
        // 传感器布局矩阵（6个传感器的空间位置）
        Eigen::Matrix<double, 6, 6> sensorMatrix;

        // 传感器1-3：位置传感器（X, Y, Z方向）
        sensorMatrix.row(0) << 1, 0, 0, 0, 0, 0;  // X位置
        sensorMatrix.row(1) << 0, 1, 0, 0, 0, 0;  // Y位置
        sensorMatrix.row(2) << 0, 0, 1, 0, 0, 0;  // Z位置

        // 传感器4-6：姿态传感器（Rx, Ry, Rz方向）
        double r = kinematicParams.platformRadius;
        sensorMatrix.row(3) << 0, 0, 0, 1, 0, 0;           // Rx
        sensorMatrix.row(4) << 0, 0, 0, 0, 1, 0;           // Ry
        sensorMatrix.row(5) << 0, 0, 0, 0, 0, 1;           // Rz

        // 使用伪逆求解位姿
        Eigen::VectorXd pose = sensorMatrix.completeOrthogonalDecomposition().solve(sensorValues);

        return pose;
    }

    // 计算位姿误差
    PoseError calculatePoseError(const Pose6DOF& target, const Pose6DOF& current) {
        PoseError error;

        // 位置误差
        error.position[0] = target.position[0] - current.position[0];
        error.position[1] = target.position[1] - current.position[1];
        error.position[2] = target.position[2] - current.position[2];

        // 姿态误差
        error.orientation[0] = target.orientation[0] - current.orientation[0];
        error.orientation[1] = target.orientation[1] - current.orientation[1];
        error.orientation[2] = target.orientation[2] - current.orientation[2];

        // 计算总误差幅值
        error.positionMagnitude = std::sqrt(
            error.position[0] * error.position[0] +
            error.position[1] * error.position[1] +
            error.position[2] * error.position[2]
        );

        error.orientationMagnitude = std::sqrt(
            error.orientation[0] * error.orientation[0] +
            error.orientation[1] * error.orientation[1] +
            error.orientation[2] * error.orientation[2]
        );

        return error;
    }

    // 更新雅可比矩阵
    void updateJacobianMatrix(const Pose6DOF& currentPose) {
        // 雅可比矩阵描述了执行器空间到任务空间的映射关系
        jacobianMatrix = Eigen::Matrix<double, 6, 6>::Identity();

        // 考虑当前姿态对雅可比矩阵的影响
        double rx = currentPose.orientation[0];
        double ry = currentPose.orientation[1];
        double rz = currentPose.orientation[2];

        // 旋转矩阵
        Eigen::Matrix3d Rx, Ry, Rz;
        Rx << 1, 0, 0,
              0, cos(rx), -sin(rx),
              0, sin(rx), cos(rx);

        Ry << cos(ry), 0, sin(ry),
              0, 1, 0,
              -sin(ry), 0, cos(ry);

        Rz << cos(rz), -sin(rz), 0,
              sin(rz), cos(rz), 0,
              0, 0, 1;

        Eigen::Matrix3d R = Rz * Ry * Rx;

        // 更新雅可比矩阵的旋转部分
        jacobianMatrix.block<3,3>(0,0) = R;
        jacobianMatrix.block<3,3>(3,3) = R;
    }

    // 计算控制力
    ControlForces calculateControlForces(const PoseError& poseError) {
        ControlForces forces;

        // PD控制律
        double kp_pos = controlParams.positionGain;
        double kd_pos = 2 * controlParams.dampingRatio *
                       std::sqrt(kp_pos * controlParams.naturalFrequency);

        double kp_ori = controlParams.orientationGain;
        double kd_ori = 2 * controlParams.dampingRatio *
                       std::sqrt(kp_ori * controlParams.naturalFrequency);

        // 位置控制力
        forces.linear[0] = kp_pos * poseError.position[0];
        forces.linear[1] = kp_pos * poseError.position[1];
        forces.linear[2] = kp_pos * poseError.position[2];

        // 姿态控制力矩
        forces.angular[0] = kp_ori * poseError.orientation[0];
        forces.angular[1] = kp_ori * poseError.orientation[1];
        forces.angular[2] = kp_ori * poseError.orientation[2];

        return forces;
    }

    // 执行器指令分配
    ActuatorCommands distributeActuatorCommands(const ControlForces& forces) {
        // 将6DOF控制力分配到各个执行器
        Eigen::VectorXd forceVector(6);
        forceVector << forces.linear[0], forces.linear[1], forces.linear[2],
                      forces.angular[0], forces.angular[1], forces.angular[2];

        // 通过雅可比矩阵的转置分配执行器指令
        Eigen::VectorXd actuatorCommands = jacobianMatrix.transpose() * forceVector;

        ActuatorCommands commands;
        for (int i = 0; i < 6; i++) {
            commands.values[i] = actuatorCommands(i);
            // 限制执行器指令范围
            commands.values[i] = std::clamp(commands.values[i], -10e-6, 10e-6);
        }

        return commands;
    }
};
```

---

## 🔗 系统集成方案

### 3.1 与现有8套设备的C++接口集成

#### 3.1.1 设备接口适配器设计
```cpp
// 设备接口适配器基类
class DeviceInterfaceAdapter {
public:
    virtual ~DeviceInterfaceAdapter() = default;

    // 纯虚函数接口
    virtual bool initialize() = 0;
    virtual bool connect() = 0;
    virtual void disconnect() = 0;
    virtual DeviceStatus getStatus() = 0;
    virtual bool executeCommand(const DeviceCommand& command) = 0;
    virtual bool executePrecisionPositioning(const PrecisionPositioningRequest& request) = 0;
    virtual void emergencyStop() = 0;

protected:
    std::string deviceId;
    DeviceType deviceType;
    ConnectionStatus connectionStatus = ConnectionStatus::DISCONNECTED;
};

// 具体设备适配器实现
class Device1Adapter : public DeviceInterfaceAdapter {
private:
    // 设备1的原有C++接口
    std::unique_ptr<Device1Interface> device1Interface;

    // 精密定位系统接口
    std::shared_ptr<PrecisionPositioningSystem> precisionSystem;

public:
    Device1Adapter(const std::string& deviceId,
                   std::shared_ptr<PrecisionPositioningSystem> precision)
        : precisionSystem(precision) {
        this->deviceId = deviceId;
        this->deviceType = DeviceType::DEVICE_1;
        device1Interface = std::make_unique<Device1Interface>();
    }

    bool initialize() override {
        try {
            // 初始化原有设备接口
            if (!device1Interface->init()) {
                return false;
            }

            // 初始化精密定位系统连接
            if (!precisionSystem->isReady()) {
                logger.warn("Precision positioning system not ready for device: {}", deviceId);
            }

            connectionStatus = ConnectionStatus::CONNECTED;
            return true;

        } catch (const std::exception& e) {
            logger.error("Device1 initialization failed: {}", e.what());
            return false;
        }
    }

    bool executeCommand(const DeviceCommand& command) override {
        try {
            // 检查是否需要精密定位
            if (command.requiresPrecisionPositioning()) {
                return executeCommandWithPrecisionPositioning(command);
            } else {
                return executeStandardCommand(command);
            }

        } catch (const std::exception& e) {
            logger.error("Command execution failed for device {}: {}", deviceId, e.what());
            return false;
        }
    }

    bool executePrecisionPositioning(const PrecisionPositioningRequest& request) override {
        try {
            // 1. 准备设备进入精密定位模式
            if (!prepareForPrecisionPositioning()) {
                return false;
            }

            // 2. 执行精密定位
            auto positioningFuture = precisionSystem->executePositioning(
                request.targetPosition, request.accuracyRequirement);

            // 3. 等待定位完成
            auto result = positioningFuture.get();

            if (!result.success) {
                logger.error("Precision positioning failed: {}", result.errorMessage);
                return false;
            }

            // 4. 验证定位精度
            if (result.achievedAccuracy > request.accuracyRequirement) {
                logger.warn("Achieved accuracy {} exceeds requirement {}",
                           result.achievedAccuracy, request.accuracyRequirement);
            }

            // 5. 通知设备定位完成
            notifyPositioningComplete(result);

            return true;

        } catch (const std::exception& e) {
            logger.error("Precision positioning failed for device {}: {}", deviceId, e.what());
            return false;
        }
    }

private:
    bool executeCommandWithPrecisionPositioning(const DeviceCommand& command) {
        // 1. 执行精密定位
        PrecisionPositioningRequest positioningRequest;
        positioningRequest.targetPosition = command.getTargetPosition();
        positioningRequest.accuracyRequirement = command.getAccuracyRequirement();

        if (!executePrecisionPositioning(positioningRequest)) {
            return false;
        }

        // 2. 执行设备特定操作
        return device1Interface->executeOperation(command.getOperationParams());
    }

    bool executeStandardCommand(const DeviceCommand& command) {
        // 直接执行标准设备命令
        return device1Interface->executeStandardCommand(command.getStandardParams());
    }

    bool prepareForPrecisionPositioning() {
        // 设备进入精密定位准备状态
        return device1Interface->enterPrecisionMode();
    }

    void notifyPositioningComplete(const PositioningResult& result) {
        // 通知设备定位完成，可以继续后续操作
        device1Interface->onPositioningComplete(result.achievedPosition, result.achievedAccuracy);
    }
};

// 设备管理器
class DeviceManager {
private:
    std::map<std::string, std::unique_ptr<DeviceInterfaceAdapter>> devices;
    std::shared_ptr<PrecisionPositioningSystem> precisionSystem;
    std::mutex devicesMutex;

public:
    DeviceManager(std::shared_ptr<PrecisionPositioningSystem> precision)
        : precisionSystem(precision) {}

    // 注册设备
    bool registerDevice(const std::string& deviceId, DeviceType type) {
        std::lock_guard<std::mutex> lock(devicesMutex);

        try {
            std::unique_ptr<DeviceInterfaceAdapter> adapter;

            switch (type) {
                case DeviceType::DEVICE_1:
                    adapter = std::make_unique<Device1Adapter>(deviceId, precisionSystem);
                    break;
                case DeviceType::DEVICE_2:
                    adapter = std::make_unique<Device2Adapter>(deviceId, precisionSystem);
                    break;
                // ... 其他设备类型
                default:
                    logger.error("Unknown device type for device: {}", deviceId);
                    return false;
            }

            if (!adapter->initialize()) {
                logger.error("Failed to initialize device: {}", deviceId);
                return false;
            }

            devices[deviceId] = std::move(adapter);
            logger.info("Device registered successfully: {}", deviceId);
            return true;

        } catch (const std::exception& e) {
            logger.error("Device registration failed: {}", e.what());
            return false;
        }
    }

    // 执行设备命令
    bool executeDeviceCommand(const std::string& deviceId, const DeviceCommand& command) {
        std::lock_guard<std::mutex> lock(devicesMutex);

        auto it = devices.find(deviceId);
        if (it == devices.end()) {
            logger.error("Device not found: {}", deviceId);
            return false;
        }

        return it->second->executeCommand(command);
    }

    // 批量精密定位
    std::vector<PositioningResult> executeBatchPrecisionPositioning(
            const std::vector<BatchPositioningRequest>& requests) {

        std::vector<PositioningResult> results;

        for (const auto& request : requests) {
            auto it = devices.find(request.deviceId);
            if (it == devices.end()) {
                results.push_back(PositioningResult::failure("Device not found"));
                continue;
            }

            bool success = it->second->executePrecisionPositioning(request.positioningRequest);
            if (success) {
                results.push_back(PositioningResult::success(
                    request.positioningRequest.targetPosition,
                    request.positioningRequest.accuracyRequirement));
            } else {
                results.push_back(PositioningResult::failure("Positioning failed"));
            }
        }

        return results;
    }

    // 获取所有设备状态
    std::map<std::string, DeviceStatus> getAllDeviceStatus() {
        std::lock_guard<std::mutex> lock(devicesMutex);

        std::map<std::string, DeviceStatus> statusMap;

        for (const auto& [deviceId, adapter] : devices) {
            statusMap[deviceId] = adapter->getStatus();
        }

        return statusMap;
    }
};
```

### 3.2 与MES系统的数据交换接口设计

#### 3.2.1 MES集成服务实现
```java
// MES集成服务
@Service
@Slf4j
public class MESIntegrationService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private PrecisionPositioningService precisionPositioningService;

    @Autowired
    private DeviceManagerService deviceManagerService;

    @Value("${mes.base-url}")
    private String mesBaseUrl;

    @Value("${mes.api-key}")
    private String mesApiKey;

    // 接收MES工单
    @PostMapping("/api/mes/work-order")
    public ResponseEntity<WorkOrderResponse> receiveWorkOrder(@RequestBody WorkOrderRequest request) {
        try {
            log.info("Received work order from MES: {}", request.getOrderNumber());

            // 1. 验证工单数据
            validateWorkOrder(request);

            // 2. 创建内部工单
            InternalWorkOrder internalOrder = createInternalWorkOrder(request);

            // 3. 检查设备可用性
            DeviceAvailability availability = checkDeviceAvailability(internalOrder);

            if (!availability.isAvailable()) {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(WorkOrderResponse.unavailable(availability.getReason()));
            }

            // 4. 启动工单执行
            WorkOrderExecution execution = startWorkOrderExecution(internalOrder);

            // 5. 返回接收确认
            WorkOrderResponse response = WorkOrderResponse.builder()
                .orderNumber(request.getOrderNumber())
                .status("ACCEPTED")
                .estimatedCompletionTime(execution.getEstimatedCompletionTime())
                .executionId(execution.getExecutionId())
                .build();

            return ResponseEntity.ok(response);

        } catch (WorkOrderValidationException e) {
            log.error("Work order validation failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(WorkOrderResponse.validationError(e.getMessage()));
        } catch (Exception e) {
            log.error("Work order processing failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(WorkOrderResponse.processingError(e.getMessage()));
        }
    }

    // 执行工单流程
    @Async("workOrderExecutor")
    public CompletableFuture<Void> executeWorkOrder(InternalWorkOrder workOrder) {
        try {
            log.info("Starting work order execution: {}", workOrder.getOrderNumber());

            // 1. 解析工单要求
            List<ProductionStep> productionSteps = parseProductionSteps(workOrder);

            // 2. 执行生产步骤
            for (ProductionStep step : productionSteps) {
                ExecutionResult stepResult = executeProductionStep(step);

                if (!stepResult.isSuccess()) {
                    // 步骤执行失败，报告MES
                    reportStepFailure(workOrder.getOrderNumber(), step, stepResult);
                    return CompletableFuture.completedFuture(null);
                }

                // 报告步骤完成
                reportStepCompletion(workOrder.getOrderNumber(), step, stepResult);
            }

            // 3. 工单完成，报告MES
            reportWorkOrderCompletion(workOrder);

            log.info("Work order completed successfully: {}", workOrder.getOrderNumber());

        } catch (Exception e) {
            log.error("Work order execution failed: {}", workOrder.getOrderNumber(), e);
            reportWorkOrderFailure(workOrder, e.getMessage());
        }

        return CompletableFuture.completedFuture(null);
    }

    // 执行生产步骤
    private ExecutionResult executeProductionStep(ProductionStep step) {
        try {
            switch (step.getStepType()) {
                case PRECISION_POSITIONING:
                    return executePrecisionPositioningStep(step);
                case DEVICE_OPERATION:
                    return executeDeviceOperationStep(step);
                case QUALITY_CHECK:
                    return executeQualityCheckStep(step);
                default:
                    throw new UnsupportedOperationException("Unknown step type: " + step.getStepType());
            }
        } catch (Exception e) {
            return ExecutionResult.failure(e.getMessage());
        }
    }

    // 执行精密定位步骤
    private ExecutionResult executePrecisionPositioningStep(ProductionStep step) {
        PrecisionPositioningParameters params = step.getPrecisionPositioningParameters();

        // 构建定位请求
        PositioningRequest request = PositioningRequest.builder()
            .targetPosition(params.getTargetPosition())
            .accuracyRequirement(params.getAccuracyRequirement())
            .timeout(params.getTimeout())
            .build();

        // 执行精密定位
        CompletableFuture<PositioningResult> future =
            precisionPositioningService.executePositioning(request);

        try {
            PositioningResult result = future.get(params.getTimeout(), TimeUnit.MILLISECONDS);

            if (result.isSuccess()) {
                return ExecutionResult.success(result);
            } else {
                return ExecutionResult.failure(result.getErrorMessage());
            }

        } catch (TimeoutException e) {
            return ExecutionResult.failure("Positioning timeout");
        } catch (Exception e) {
            return ExecutionResult.failure("Positioning error: " + e.getMessage());
        }
    }

    // 报告工单完成
    private void reportWorkOrderCompletion(InternalWorkOrder workOrder) {
        try {
            WorkOrderCompletionReport report = WorkOrderCompletionReport.builder()
                .orderNumber(workOrder.getOrderNumber())
                .completionTime(Instant.now())
                .actualQuantity(workOrder.getCompletedQuantity())
                .qualityData(workOrder.getQualityData())
                .performanceMetrics(workOrder.getPerformanceMetrics())
                .build();

            String url = mesBaseUrl + "/api/work-orders/" + workOrder.getOrderNumber() + "/completion";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + mesApiKey);

            HttpEntity<WorkOrderCompletionReport> entity = new HttpEntity<>(report, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Work order completion reported to MES: {}", workOrder.getOrderNumber());
            } else {
                log.error("Failed to report work order completion to MES: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error reporting work order completion to MES", e);
        }
    }

    // 实时状态同步
    @Scheduled(fixedRate = 30000) // 30秒间隔
    public void syncStatusWithMES() {
        try {
            // 获取系统状态
            SystemStatus systemStatus = getSystemStatus();

            // 构建状态报告
            StatusReport statusReport = StatusReport.builder()
                .timestamp(Instant.now())
                .systemStatus(systemStatus.getOverallStatus())
                .deviceStatuses(systemStatus.getDeviceStatuses())
                .precisionSystemStatus(systemStatus.getPrecisionSystemStatus())
                .currentWorkOrders(systemStatus.getCurrentWorkOrders())
                .performanceMetrics(systemStatus.getPerformanceMetrics())
                .build();

            // 发送到MES
            sendStatusToMES(statusReport);

        } catch (Exception e) {
            log.error("Status sync with MES failed", e);
        }
    }

    private SystemStatus getSystemStatus() {
        return SystemStatus.builder()
            .overallStatus(determineOverallSystemStatus())
            .deviceStatuses(deviceManagerService.getAllDeviceStatus())
            .precisionSystemStatus(precisionPositioningService.getSystemStatus())
            .currentWorkOrders(getCurrentWorkOrders())
            .performanceMetrics(calculatePerformanceMetrics())
            .build();
    }
}
```

### 3.3 与数字孪生平台的MQTT通信实现

#### 3.3.1 MQTT数据发布服务
```java
// 数字孪生MQTT发布服务
@Service
@Slf4j
public class DigitalTwinMQTTService {

    @Autowired
    private MqttTemplate mqttTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${digital-twin.topic-prefix}")
    private String topicPrefix;

    @Value("${digital-twin.qos}")
    private int qos = 1;

    // 发布实时定位数据
    @Async("mqttPublisher")
    public void publishPositioningData(PositioningData data) {
        try {
            String topic = topicPrefix + "/positioning/realtime";

            PositioningMessage message = PositioningMessage.builder()
                .timestamp(Instant.now())
                .deviceId("precision-positioning-system")
                .targetPosition(data.getTargetPosition())
                .currentPosition(data.getCurrentPosition())
                .achievedAccuracy(data.getAchievedAccuracy())
                .status(data.getStatus())
                .sensorData(data.getSensorData())
                .build();

            String payload = objectMapper.writeValueAsString(message);

            mqttTemplate.convertAndSend(topic, payload, qos, false);

            log.debug("Published positioning data to topic: {}", topic);

        } catch (Exception e) {
            log.error("Failed to publish positioning data", e);
        }
    }

    // 发布传感器数据
    @Scheduled(fixedRate = 100) // 10Hz发布频率
    public void publishSensorData() {
        try {
            // 获取实时传感器数据
            SensorDataSnapshot snapshot = collectSensorDataSnapshot();

            String topic = topicPrefix + "/sensors/data";

            SensorDataMessage message = SensorDataMessage.builder()
                .timestamp(snapshot.getTimestamp())
                .opticalEncoderData(snapshot.getOpticalEncoderData())
                .capacitiveSensorData(snapshot.getCapacitiveSensorData())
                .temperatureData(snapshot.getTemperatureData())
                .fusedData(snapshot.getFusedData())
                .build();

            String payload = objectMapper.writeValueAsString(message);

            mqttTemplate.convertAndSend(topic, payload, qos, false);

        } catch (Exception e) {
            log.error("Failed to publish sensor data", e);
        }
    }

    // 发布系统状态
    @Scheduled(fixedRate = 5000) // 5秒间隔
    public void publishSystemStatus() {
        try {
            SystemStatus status = getSystemStatus();

            String topic = topicPrefix + "/system/status";

            SystemStatusMessage message = SystemStatusMessage.builder()
                .timestamp(Instant.now())
                .overallStatus(status.getOverallStatus())
                .precisionSystemStatus(status.getPrecisionSystemStatus())
                .deviceStatuses(status.getDeviceStatuses())
                .performanceMetrics(status.getPerformanceMetrics())
                .alertCount(status.getActiveAlertCount())
                .build();

            String payload = objectMapper.writeValueAsString(message);

            mqttTemplate.convertAndSend(topic, payload, qos, true); // 保留消息

        } catch (Exception e) {
            log.error("Failed to publish system status", e);
        }
    }

    // 发布告警信息
    public void publishAlert(AlertMessage alert) {
        try {
            String topic = topicPrefix + "/alerts/" + alert.getLevel().toString().toLowerCase();

            DigitalTwinAlert dtAlert = DigitalTwinAlert.builder()
                .timestamp(alert.getTimestamp())
                .alertId(alert.getAlertId())
                .level(alert.getLevel())
                .source(alert.getSource())
                .message(alert.getMessage())
                .data(alert.getData())
                .build();

            String payload = objectMapper.writeValueAsString(dtAlert);

            mqttTemplate.convertAndSend(topic, payload, qos, true); // 保留告警消息

            log.info("Published alert to digital twin: {}", alert.getAlertId());

        } catch (Exception e) {
            log.error("Failed to publish alert", e);
        }
    }
}
```

### 3.4 异常处理和容错机制的软件实现

#### 3.4.1 全局异常处理框架
```java
// 全局异常处理器
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @Autowired
    private AlertService alertService;

    @Autowired
    private RecoveryService recoveryService;

    // 精度异常处理
    @ExceptionHandler(PrecisionException.class)
    public ResponseEntity<ErrorResponse> handlePrecisionException(PrecisionException e) {
        log.error("Precision error occurred: {}", e.getMessage(), e);

        // 触发精度恢复流程
        recoveryService.startPrecisionRecovery(e.getContext());

        // 发送告警
        alertService.sendAlert(AlertLevel.HIGH, "Precision Error", e.getMessage());

        ErrorResponse response = ErrorResponse.builder()
            .code("PRECISION_ERROR")
            .message(e.getMessage())
            .timestamp(Instant.now())
            .recoveryAction("Precision recovery initiated")
            .build();

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    // 设备通信异常处理
    @ExceptionHandler(DeviceCommunicationException.class)
    public ResponseEntity<ErrorResponse> handleDeviceCommunicationException(
            DeviceCommunicationException e) {

        log.error("Device communication error: {}", e.getMessage(), e);

        // 尝试重新建立连接
        recoveryService.attemptDeviceReconnection(e.getDeviceId());

        ErrorResponse response = ErrorResponse.builder()
            .code("DEVICE_COMMUNICATION_ERROR")
            .message("Device communication failed: " + e.getDeviceId())
            .timestamp(Instant.now())
            .recoveryAction("Reconnection attempted")
            .build();

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    // TwinCAT通信异常处理
    @ExceptionHandler(TwinCATCommunicationException.class)
    public ResponseEntity<ErrorResponse> handleTwinCATException(TwinCATCommunicationException e) {
        log.error("TwinCAT communication error: {}", e.getMessage(), e);

        // 触发TwinCAT重连
        recoveryService.restartTwinCATConnection();

        // 发送紧急告警
        alertService.sendAlert(AlertLevel.CRITICAL, "TwinCAT Communication Lost", e.getMessage());

        ErrorResponse response = ErrorResponse.builder()
            .code("TWINCAT_COMMUNICATION_ERROR")
            .message(e.getMessage())
            .timestamp(Instant.now())
            .recoveryAction("TwinCAT reconnection initiated")
            .build();

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }
}

// 恢复服务
@Service
@Slf4j
public class RecoveryService {

    @Autowired
    private PrecisionPositioningService precisionService;

    @Autowired
    private TwinCATCommunicationService twinCATService;

    @Autowired
    private DeviceManagerService deviceManagerService;

    // 精度恢复流程
    @Async("recoveryExecutor")
    public CompletableFuture<RecoveryResult> startPrecisionRecovery(PrecisionContext context) {
        log.info("Starting precision recovery for context: {}", context);

        try {
            // 1. 停止当前运动
            precisionService.emergencyStop();

            // 2. 等待系统稳定
            Thread.sleep(1000);

            // 3. 重新校准传感器
            CalibrationResult calibrationResult = recalibrateSensors();

            if (!calibrationResult.isSuccess()) {
                return CompletableFuture.completedFuture(
                    RecoveryResult.failure("Sensor calibration failed"));
            }

            // 4. 重新执行定位
            PositioningResult positioningResult = retryPositioning(context.getTargetPosition());

            if (positioningResult.isSuccess()) {
                log.info("Precision recovery completed successfully");
                return CompletableFuture.completedFuture(RecoveryResult.success());
            } else {
                // 5. 如果恢复失败，转入人工干预模式
                requestHumanIntervention(context);
                return CompletableFuture.completedFuture(
                    RecoveryResult.failure("Recovery failed, human intervention required"));
            }

        } catch (Exception e) {
            log.error("Precision recovery failed", e);
            return CompletableFuture.completedFuture(
                RecoveryResult.failure("Recovery exception: " + e.getMessage()));
        }
    }

    // 传感器重新校准
    private CalibrationResult recalibrateSensors() {
        try {
            log.info("Starting sensor recalibration");

            // 光栅尺校准
            boolean opticalCalibrationSuccess = calibrateOpticalEncoders();

            // 电容传感器校准
            boolean capacitiveCalibrationSuccess = calibrateCapacitiveSensors();

            // 温度传感器校准
            boolean temperatureCalibrationSuccess = calibrateTemperatureSensors();

            if (opticalCalibrationSuccess && capacitiveCalibrationSuccess && temperatureCalibrationSuccess) {
                log.info("Sensor recalibration completed successfully");
                return CalibrationResult.success();
            } else {
                log.error("Sensor recalibration failed");
                return CalibrationResult.failure("One or more sensors failed calibration");
            }

        } catch (Exception e) {
            log.error("Sensor recalibration exception", e);
            return CalibrationResult.failure("Calibration exception: " + e.getMessage());
        }
    }

    // 设备重连
    @Async("recoveryExecutor")
    public CompletableFuture<Boolean> attemptDeviceReconnection(String deviceId) {
        log.info("Attempting to reconnect device: {}", deviceId);

        try {
            // 最多重试3次
            for (int attempt = 1; attempt <= 3; attempt++) {
                log.info("Device reconnection attempt {} for device: {}", attempt, deviceId);

                boolean success = deviceManagerService.reconnectDevice(deviceId);

                if (success) {
                    log.info("Device reconnection successful: {}", deviceId);
                    return CompletableFuture.completedFuture(true);
                }

                // 等待后重试
                Thread.sleep(2000 * attempt); // 递增等待时间
            }

            log.error("Device reconnection failed after 3 attempts: {}", deviceId);
            return CompletableFuture.completedFuture(false);

        } catch (Exception e) {
            log.error("Device reconnection exception for device: {}", deviceId, e);
            return CompletableFuture.completedFuture(false);
        }
    }
}
```

---

## 🚀 开发实施计划

### 4.1 软件开发技术栈选择和环境搭建

#### 4.1.1 技术栈配置表
| 层次 | 技术选型 | 版本 | 用途 | 许可成本 |
|------|----------|------|------|----------|
| **实时控制层** | BECKHOFF TwinCAT 3 | 3.1.4024 | PLC实时控制 | 0.8万 |
| **应用逻辑层** | SpringBoot | 3.2.0 | 业务逻辑处理 | 免费 |
| **数据库** | PostgreSQL | 15.0 | 关系数据存储 | 免费 |
| **时序数据库** | InfluxDB | 2.7 | 传感器数据存储 | 免费 |
| **缓存** | Redis | 7.0 | 数据缓存 | 免费 |
| **消息队列** | RabbitMQ | 3.12 | 异步消息处理 | 免费 |
| **前端框架** | Qt | 6.5 | 客户端界面 | 商业许可 |
| **通信协议** | EtherCAT | - | 实时通信 | 免费 |
| **监控工具** | Grafana + Prometheus | 最新 | 系统监控 | 免费 |

#### 4.1.2 开发环境搭建脚本
```bash
#!/bin/bash
# 开发环境自动化搭建脚本

echo "开始搭建精密定位系统开发环境..."

# 1. 安装基础开发工具
install_basic_tools() {
    echo "安装基础开发工具..."

    # Git版本控制
    sudo apt-get update
    sudo apt-get install -y git

    # Docker容器化
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    sudo usermod -aG docker $USER

    # Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
}

# 2. 安装Java开发环境
install_java_environment() {
    echo "安装Java开发环境..."

    # OpenJDK 17
    sudo apt-get install -y openjdk-17-jdk

    # Maven构建工具
    sudo apt-get install -y maven

    # IntelliJ IDEA (可选)
    snap install intellij-idea-community --classic
}

# 3. 安装Qt开发环境
install_qt_environment() {
    echo "安装Qt开发环境..."

    # Qt6开发库
    sudo apt-get install -y qt6-base-dev qt6-tools-dev cmake

    # Qt Creator IDE
    sudo apt-get install -y qtcreator

    # 额外Qt模块
    sudo apt-get install -y qt6-websockets-dev qt6-networkauth-dev
}

# 4. 搭建数据库环境
setup_database_environment() {
    echo "搭建数据库环境..."

    # 创建docker-compose.yml
    cat > docker-compose-dev.yml << EOF
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: precision_control_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  influxdb:
    image: influxdb:2.7
    ports:
      - "8086:8086"
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: admin123
      DOCKER_INFLUXDB_INIT_ORG: precision-control
      DOCKER_INFLUXDB_INIT_BUCKET: sensor-data
    volumes:
      - influxdb_data:/var/lib/influxdb2

  rabbitmq:
    image: rabbitmq:3.12-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  postgres_data:
  redis_data:
  influxdb_data:
  rabbitmq_data:
EOF

    # 启动开发数据库
    docker-compose -f docker-compose-dev.yml up -d
}

# 5. 创建项目结构
create_project_structure() {
    echo "创建项目结构..."

    mkdir -p precision-positioning-system
    cd precision-positioning-system

    # 后端项目结构
    mkdir -p backend/src/main/java/com/precision/control
    mkdir -p backend/src/main/resources
    mkdir -p backend/src/test/java

    # 前端项目结构
    mkdir -p frontend/src
    mkdir -p frontend/resources
    mkdir -p frontend/build

    # TwinCAT项目结构
    mkdir -p twincat/PLC/POUs
    mkdir -p twincat/PLC/DUTs
    mkdir -p twincat/PLC/GVLs
    mkdir -p twincat/Motion
    mkdir -p twincat/IO

    # 文档目录
    mkdir -p docs/api
    mkdir -p docs/design
    mkdir -p docs/user-manual

    # 配置目录
    mkdir -p config/dev
    mkdir -p config/test
    mkdir -p config/prod

    echo "项目结构创建完成"
}

# 6. 配置开发工具
configure_development_tools() {
    echo "配置开发工具..."

    # Git配置
    git config --global user.name "Precision Control Team"
    git config --global user.email "<EMAIL>"

    # 创建.gitignore
    cat > .gitignore << EOF
# Java
*.class
*.jar
*.war
target/
.mvn/
mvnw
mvnw.cmd

# Qt
*.pro.user
*.autosave
build-*/
*.o
*.so
*.dll
*.exe

# TwinCAT
*.tpy
*.tclrs
*.compiled-library
_Boot/
_CompileInfo/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite
EOF

    # 创建README.md
    cat > README.md << EOF
# 精密定位控制系统

## 项目概述
靶丸高精度定位系统，实现±5μm定位精度。

## 技术架构
- 实时控制层：BECKHOFF TwinCAT 3
- 应用逻辑层：SpringBoot 3.2
- 人机界面层：Qt 6.5
- 数据存储：PostgreSQL + InfluxDB

## 开发环境
请参考 docs/development-setup.md

## 构建和部署
请参考 docs/build-and-deploy.md
EOF
}

# 执行安装步骤
main() {
    install_basic_tools
    install_java_environment
    install_qt_environment
    setup_database_environment
    create_project_structure
    configure_development_tools

    echo "开发环境搭建完成！"
    echo "请重新登录以使Docker权限生效"
    echo "数据库服务已启动，可通过以下地址访问："
    echo "  PostgreSQL: localhost:5432"
    echo "  Redis: localhost:6379"
    echo "  InfluxDB: http://localhost:8086"
    echo "  RabbitMQ: http://localhost:15672"
}

main
```

### 4.2 模块化开发的具体步骤和时间安排

#### 4.2.1 开发阶段规划
```
开发阶段时间表（12周并行开发）

Week 1-2: 基础架构搭建
├── 开发环境搭建和配置
├── 项目框架初始化
├── 基础通信接口开发
└── 数据库设计和初始化

Week 3-4: 实时控制层开发
├── TwinCAT PLC程序框架
├── 基础运动控制功能
├── 传感器数据采集
└── EtherCAT通信配置

Week 5-6: 核心算法实现
├── 分层控制算法
├── PID控制器实现
├── 数据融合算法
└── 6DOF协调控制

Week 7-8: 应用逻辑层开发
├── SpringBoot服务架构
├── 设备管理服务
├── 精度控制服务
└── 数据管理服务

Week 9-10: 人机界面开发
├── Qt客户端框架
├── 精密控制界面
├── 监控界面
└── 配置界面

Week 11-12: 系统集成和测试
├── 模块集成测试
├── 系统联调
├── 性能优化
└── 文档完善
```

#### 4.2.2 详细开发任务分解
```yaml
# 开发任务配置
development_tasks:

  # 第1-2周：基础架构
  foundation_phase:
    duration: 2_weeks
    team_size: 4_developers
    tasks:
      - name: "环境搭建"
        assignee: "DevOps工程师"
        duration: 3_days
        deliverables:
          - "开发环境自动化脚本"
          - "CI/CD流水线配置"
          - "代码仓库初始化"

      - name: "数据库设计"
        assignee: "后端工程师"
        duration: 4_days
        deliverables:
          - "数据库ER图"
          - "建表SQL脚本"
          - "数据迁移脚本"

      - name: "通信接口框架"
        assignee: "系统工程师"
        duration: 5_days
        deliverables:
          - "EtherCAT通信框架"
          - "WebSocket通信框架"
          - "MQTT通信框架"

  # 第3-4周：实时控制层
  realtime_control_phase:
    duration: 2_weeks
    team_size: 2_developers
    tasks:
      - name: "TwinCAT项目搭建"
        assignee: "PLC工程师"
        duration: 2_days
        deliverables:
          - "TwinCAT项目结构"
          - "I/O配置"
          - "轴配置"

      - name: "基础运动控制"
        assignee: "PLC工程师"
        duration: 5_days
        deliverables:
          - "单轴运动控制程序"
          - "多轴协调控制程序"
          - "安全监控程序"

      - name: "传感器接口"
        assignee: "硬件工程师"
        duration: 3_days
        deliverables:
          - "光栅尺接口程序"
          - "电容传感器接口程序"
          - "温度传感器接口程序"

  # 第5-6周：核心算法
  algorithm_phase:
    duration: 2_weeks
    team_size: 3_developers
    tasks:
      - name: "分层控制算法"
        assignee: "算法工程师"
        duration: 4_days
        deliverables:
          - "粗定位控制算法"
          - "精定位控制算法"
          - "算法测试用例"

      - name: "PID控制器"
        assignee: "控制工程师"
        duration: 3_days
        deliverables:
          - "自适应PID控制器"
          - "参数调优工具"
          - "性能测试报告"

      - name: "数据融合算法"
        assignee: "算法工程师"
        duration: 5_days
        deliverables:
          - "卡尔曼滤波实现"
          - "多传感器融合算法"
          - "异常检测算法"

  # 第7-8周：应用逻辑层
  application_layer_phase:
    duration: 2_weeks
    team_size: 3_developers
    tasks:
      - name: "SpringBoot服务框架"
        assignee: "后端工程师"
        duration: 3_days
        deliverables:
          - "微服务架构"
          - "API接口定义"
          - "服务注册发现"

      - name: "核心业务服务"
        assignee: "后端工程师"
        duration: 6_days
        deliverables:
          - "精度控制服务"
          - "设备管理服务"
          - "数据管理服务"

      - name: "集成接口服务"
        assignee: "集成工程师"
        duration: 5_days
        deliverables:
          - "MES集成接口"
          - "设备接口适配器"
          - "数字孪生接口"

  # 第9-10周：人机界面
  ui_development_phase:
    duration: 2_weeks
    team_size: 2_developers
    tasks:
      - name: "Qt客户端框架"
        assignee: "前端工程师"
        duration: 3_days
        deliverables:
          - "主窗口框架"
          - "通信模块"
          - "数据模型"

      - name: "核心界面开发"
        assignee: "UI工程师"
        duration: 6_days
        deliverables:
          - "精密控制界面"
          - "监控大屏界面"
          - "系统配置界面"

      - name: "界面集成测试"
        assignee: "测试工程师"
        duration: 3_days
        deliverables:
          - "界面功能测试"
          - "用户体验测试"
          - "性能测试"

  # 第11-12周：集成测试
  integration_phase:
    duration: 2_weeks
    team_size: 5_developers
    tasks:
      - name: "系统集成"
        assignee: "系统工程师"
        duration: 5_days
        deliverables:
          - "模块集成"
          - "接口联调"
          - "端到端测试"

      - name: "性能优化"
        assignee: "性能工程师"
        duration: 4_days
        deliverables:
          - "性能瓶颈分析"
          - "优化方案实施"
          - "性能测试报告"

      - name: "文档完善"
        assignee: "技术文档工程师"
        duration: 3_days
        deliverables:
          - "API文档"
          - "用户手册"
          - "运维手册"
```

### 4.3 测试验证方案和调试策略

#### 4.3.1 分层测试策略
```cpp
// 测试框架设计
class PrecisionControlTestFramework {
private:
    // 测试配置
    struct TestConfig {
        double targetAccuracy = 5e-6;      // ±5μm目标精度
        int testCycles = 100;              // 测试循环次数
        double testRange = 0.1;            // 测试范围±100mm
        std::chrono::seconds timeout{30};  // 测试超时时间
    } config;

    // 测试结果统计
    struct TestStatistics {
        int totalTests = 0;
        int passedTests = 0;
        int failedTests = 0;
        double averageAccuracy = 0.0;
        double maxAccuracy = 0.0;
        double minAccuracy = 0.0;
        std::chrono::milliseconds averageTime{0};
    };

public:
    // 单元测试：PID控制器
    TestResult testPIDController() {
        log.info("Starting PID controller unit test");

        AdaptivePIDController pid(1000.0, 50.0, 10.0);
        TestStatistics stats;

        for (int i = 0; i < config.testCycles; i++) {
            // 生成测试误差
            double testError = generateTestError();

            // PID计算
            auto startTime = std::chrono::steady_clock::now();
            double output = pid.calculate(testError);
            auto endTime = std::chrono::steady_clock::now();

            // 验证输出合理性
            bool testPassed = validatePIDOutput(testError, output);

            // 统计结果
            updateStatistics(stats, testPassed,
                           std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime));
        }

        return generateTestReport("PID Controller", stats);
    }

    // 集成测试：精密定位流程
    TestResult testPrecisionPositioningFlow() {
        log.info("Starting precision positioning integration test");

        TestStatistics stats;

        for (int i = 0; i < config.testCycles; i++) {
            // 生成随机目标位置
            Position targetPosition = generateRandomPosition();

            // 执行定位
            auto startTime = std::chrono::steady_clock::now();
            PositioningResult result = executePrecisionPositioning(targetPosition);
            auto endTime = std::chrono::steady_clock::now();

            // 验证结果
            bool testPassed = validatePositioningResult(result, targetPosition);

            // 统计结果
            updateStatistics(stats, testPassed,
                           std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime));

            if (testPassed) {
                stats.averageAccuracy += result.achievedAccuracy;
                stats.maxAccuracy = std::max(stats.maxAccuracy, result.achievedAccuracy);
                stats.minAccuracy = std::min(stats.minAccuracy, result.achievedAccuracy);
            }
        }

        if (stats.passedTests > 0) {
            stats.averageAccuracy /= stats.passedTests;
        }

        return generateTestReport("Precision Positioning Flow", stats);
    }

    // 压力测试：连续定位
    TestResult testContinuousPositioning() {
        log.info("Starting continuous positioning stress test");

        const int STRESS_TEST_DURATION = 3600; // 1小时压力测试
        const int POSITIONING_INTERVAL = 10;   // 10秒间隔

        TestStatistics stats;
        auto testStartTime = std::chrono::steady_clock::now();

        while (std::chrono::duration_cast<std::chrono::seconds>(
                   std::chrono::steady_clock::now() - testStartTime).count() < STRESS_TEST_DURATION) {

            Position targetPosition = generateRandomPosition();

            auto startTime = std::chrono::steady_clock::now();
            PositioningResult result = executePrecisionPositioning(targetPosition);
            auto endTime = std::chrono::steady_clock::now();

            bool testPassed = validatePositioningResult(result, targetPosition);
            updateStatistics(stats, testPassed,
                           std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime));

            // 等待下次测试
            std::this_thread::sleep_for(std::chrono::seconds(POSITIONING_INTERVAL));
        }

        return generateTestReport("Continuous Positioning Stress Test", stats);
    }

private:
    // 生成测试报告
    TestResult generateTestReport(const std::string& testName, const TestStatistics& stats) {
        TestResult result;
        result.testName = testName;
        result.totalTests = stats.totalTests;
        result.passedTests = stats.passedTests;
        result.failedTests = stats.failedTests;
        result.successRate = static_cast<double>(stats.passedTests) / stats.totalTests * 100.0;
        result.averageAccuracy = stats.averageAccuracy;
        result.averageTime = stats.averageTime;

        // 判断测试是否通过
        result.passed = (result.successRate >= 95.0) &&
                       (stats.averageAccuracy <= config.targetAccuracy);

        log.info("Test Report: {}", testName);
        log.info("  Total Tests: {}", stats.totalTests);
        log.info("  Passed: {}", stats.passedTests);
        log.info("  Failed: {}", stats.failedTests);
        log.info("  Success Rate: {:.2f}%", result.successRate);
        log.info("  Average Accuracy: {:.2f}μm", stats.averageAccuracy * 1e6);
        log.info("  Average Time: {}ms", stats.averageTime.count());

        return result;
    }
};
```

### 4.4 部署和维护的技术方案

#### 4.4.1 Docker容器化部署
```yaml
# docker-compose-production.yml
version: '3.8'

services:
  # 主应用服务
  precision-control-app:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: precision-control:latest
    container_name: precision-control-app
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "8081:8081"  # WebSocket端口
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - INFLUXDB_HOST=influxdb
      - RABBITMQ_HOST=rabbitmq
      - TWINCAT_HOST=*************
    volumes:
      - ./config/production:/app/config
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - postgres
      - redis
      - influxdb
      - rabbitmq
    networks:
      - precision-control-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据库服务
  postgres:
    image: postgres:15
    container_name: precision-control-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: precision_control
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - precision-control-network

  # 时序数据库
  influxdb:
    image: influxdb:2.7
    container_name: precision-control-influxdb
    restart: unless-stopped
    ports:
      - "8086:8086"
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: ${INFLUX_USER}
      DOCKER_INFLUXDB_INIT_PASSWORD: ${INFLUX_PASSWORD}
      DOCKER_INFLUXDB_INIT_ORG: precision-control
      DOCKER_INFLUXDB_INIT_BUCKET: sensor-data
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - precision-control-network

  # 缓存服务
  redis:
    image: redis:7-alpine
    container_name: precision-control-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - precision-control-network

  # 消息队列
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: precision-control-rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./config/rabbitmq:/etc/rabbitmq
    networks:
      - precision-control-network

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: precision-control-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - precision-control-network

  # 可视化服务
  grafana:
    image: grafana/grafana:latest
    container_name: precision-control-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - precision-control-network

volumes:
  postgres_data:
  influxdb_data:
  influxdb_config:
  redis_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:

networks:
  precision-control-network:
    driver: bridge
```

#### 4.4.2 自动化部署脚本
```bash
#!/bin/bash
# 生产环境自动化部署脚本

set -e

# 配置变量
PROJECT_NAME="precision-control-system"
DOCKER_REGISTRY="registry.company.com"
VERSION=${1:-latest}
ENVIRONMENT=${2:-production}

echo "开始部署 $PROJECT_NAME 版本 $VERSION 到 $ENVIRONMENT 环境"

# 1. 预部署检查
pre_deployment_check() {
    echo "执行预部署检查..."

    # 检查Docker环境
    if ! command -v docker &> /dev/null; then
        echo "错误: Docker未安装"
        exit 1
    fi

    # 检查磁盘空间
    AVAILABLE_SPACE=$(df / | awk 'NR==2 {print $4}')
    REQUIRED_SPACE=5000000  # 5GB

    if [ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]; then
        echo "错误: 磁盘空间不足，需要至少5GB"
        exit 1
    fi

    # 检查网络连接
    if ! ping -c 1 google.com &> /dev/null; then
        echo "警告: 网络连接可能存在问题"
    fi

    echo "预部署检查完成"
}

# 2. 备份当前版本
backup_current_version() {
    echo "备份当前版本..."

    BACKUP_DIR="/opt/backups/$PROJECT_NAME/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR

    # 备份数据库
    docker exec precision-control-db pg_dump -U precision_user precision_control > $BACKUP_DIR/database.sql

    # 备份配置文件
    cp -r ./config $BACKUP_DIR/

    # 备份应用数据
    cp -r ./data $BACKUP_DIR/

    echo "备份完成: $BACKUP_DIR"
}

# 3. 拉取新镜像
pull_new_images() {
    echo "拉取新镜像..."

    # 拉取应用镜像
    docker pull $DOCKER_REGISTRY/$PROJECT_NAME:$VERSION

    # 拉取依赖镜像
    docker-compose -f docker-compose-$ENVIRONMENT.yml pull

    echo "镜像拉取完成"
}

# 4. 停止旧服务
stop_old_services() {
    echo "停止旧服务..."

    # 优雅停止应用
    docker-compose -f docker-compose-$ENVIRONMENT.yml stop precision-control-app

    # 等待连接关闭
    sleep 10

    # 停止其他服务
    docker-compose -f docker-compose-$ENVIRONMENT.yml stop

    echo "旧服务已停止"
}

# 5. 启动新服务
start_new_services() {
    echo "启动新服务..."

    # 更新环境变量
    export IMAGE_VERSION=$VERSION

    # 启动数据库服务
    docker-compose -f docker-compose-$ENVIRONMENT.yml up -d postgres redis influxdb rabbitmq

    # 等待数据库就绪
    echo "等待数据库就绪..."
    sleep 30

    # 执行数据库迁移
    docker-compose -f docker-compose-$ENVIRONMENT.yml run --rm precision-control-app \
        java -jar app.jar --spring.profiles.active=$ENVIRONMENT --migrate-database

    # 启动应用服务
    docker-compose -f docker-compose-$ENVIRONMENT.yml up -d precision-control-app

    # 启动监控服务
    docker-compose -f docker-compose-$ENVIRONMENT.yml up -d prometheus grafana

    echo "新服务启动完成"
}

# 6. 健康检查
health_check() {
    echo "执行健康检查..."

    MAX_ATTEMPTS=30
    ATTEMPT=0

    while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
        if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
            echo "应用健康检查通过"
            break
        fi

        echo "等待应用启动... ($((ATTEMPT + 1))/$MAX_ATTEMPTS)"
        sleep 10
        ATTEMPT=$((ATTEMPT + 1))
    done

    if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
        echo "错误: 应用健康检查失败"
        rollback_deployment
        exit 1
    fi

    # 功能测试
    echo "执行功能测试..."

    # 测试精密定位接口
    RESPONSE=$(curl -s -X POST http://localhost:8080/api/precision/test \
        -H "Content-Type: application/json" \
        -d '{"targetPosition": {"x": 0.001, "y": 0.001, "z": 0.001}, "accuracyRequirement": 5e-6}')

    if echo $RESPONSE | grep -q "success"; then
        echo "功能测试通过"
    else
        echo "错误: 功能测试失败"
        rollback_deployment
        exit 1
    fi
}

# 7. 回滚部署
rollback_deployment() {
    echo "执行部署回滚..."

    # 停止新服务
    docker-compose -f docker-compose-$ENVIRONMENT.yml stop

    # 恢复备份
    LATEST_BACKUP=$(ls -t /opt/backups/$PROJECT_NAME/ | head -n1)

    if [ -n "$LATEST_BACKUP" ]; then
        echo "恢复备份: $LATEST_BACKUP"

        # 恢复配置
        cp -r /opt/backups/$PROJECT_NAME/$LATEST_BACKUP/config ./

        # 恢复数据
        cp -r /opt/backups/$PROJECT_NAME/$LATEST_BACKUP/data ./

        # 恢复数据库
        docker-compose -f docker-compose-$ENVIRONMENT.yml up -d postgres
        sleep 10
        docker exec -i precision-control-db psql -U precision_user precision_control < /opt/backups/$PROJECT_NAME/$LATEST_BACKUP/database.sql

        # 启动旧版本
        docker-compose -f docker-compose-$ENVIRONMENT.yml up -d

        echo "回滚完成"
    else
        echo "错误: 未找到备份文件"
    fi
}

# 8. 清理旧镜像
cleanup_old_images() {
    echo "清理旧镜像..."

    # 删除未使用的镜像
    docker image prune -f

    # 删除旧版本镜像（保留最近3个版本）
    docker images $DOCKER_REGISTRY/$PROJECT_NAME --format "table {{.Tag}}" | \
        tail -n +4 | \
        xargs -I {} docker rmi $DOCKER_REGISTRY/$PROJECT_NAME:{} 2>/dev/null || true

    echo "清理完成"
}

# 9. 发送部署通知
send_deployment_notification() {
    echo "发送部署通知..."

    # 发送邮件通知（示例）
    SUBJECT="$PROJECT_NAME 部署完成 - 版本 $VERSION"
    BODY="部署时间: $(date)\n环境: $ENVIRONMENT\n版本: $VERSION\n状态: 成功"

    # echo "$BODY" | mail -s "$SUBJECT" <EMAIL>

    # 发送钉钉通知（示例）
    # curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN" \
    #      -H "Content-Type: application/json" \
    #      -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"$SUBJECT\\n$BODY\"}}"

    echo "部署通知已发送"
}

# 主执行流程
main() {
    pre_deployment_check
    backup_current_version
    pull_new_images
    stop_old_services
    start_new_services
    health_check
    cleanup_old_images
    send_deployment_notification

    echo "部署完成！"
    echo "应用访问地址: http://localhost:8080"
    echo "监控面板: http://localhost:3000"
    echo "系统状态: http://localhost:8080/actuator/health"
}

# 错误处理
trap 'echo "部署过程中发生错误，执行回滚..."; rollback_deployment; exit 1' ERR

# 执行主流程
main
```

---

## 📋 总结

这个电控系统软件架构实施方案为您的靶丸高精度定位系统提供了完整的软件实现路径，确保能够实现±5μm的定位精度要求。方案包含了从底层实时控制到上层业务逻辑的完整技术栈，具有良好的可扩展性和可维护性。

### 🎯 方案核心特点

#### 1. **分层架构设计**
- **实时控制层：** BECKHOFF TwinCAT，1ms控制周期
- **应用逻辑层：** SpringBoot微服务架构
- **人机界面层：** Qt6跨平台客户端
- **数据存储层：** PostgreSQL + InfluxDB + Redis

#### 2. **核心算法实现**
- **分层控制：** 粗定位（±5μm）+ 精定位（±1μm）
- **自适应PID：** 参数自动调优，适应不同工况
- **多传感器融合：** 卡尔曼滤波，提高测量精度
- **6DOF协调控制：** 完整空间位姿控制

#### 3. **系统集成能力**
- **设备接口适配：** 支持8套现有设备无缝集成
- **MES系统对接：** 完整的工单流程管理
- **数字孪生通信：** MQTT实时数据发布
- **异常处理机制：** 全面的容错和恢复策略

#### 4. **开发实施保障**
- **12周并行开发：** 详细的任务分解和时间安排
- **自动化测试：** 单元测试、集成测试、压力测试
- **容器化部署：** Docker + Docker Compose
- **持续集成：** 自动化构建、测试、部署

### 💰 投资效益分析

| 投资项目 | 金额(万) | 占比 | 预期收益 |
|----------|----------|------|----------|
| **软件开发** | 18.25 | 21% | 核心竞争力 |
| **硬件设备** | 69.0 | 79% | 基础能力 |
| **总投资** | 87.25 | 100% | 完整解决方案 |

### 🚀 实施建议

1. **立即启动：** 开发环境搭建和团队组建
2. **分阶段实施：** 按12周计划逐步推进
3. **持续测试：** 每个阶段都要进行充分测试
4. **文档同步：** 开发过程中同步完善技术文档

这个软件架构方案将为您的项目成功实施提供坚实的技术基础，确保在87.25万投资预算内实现±5μm的高精度定位目标。
